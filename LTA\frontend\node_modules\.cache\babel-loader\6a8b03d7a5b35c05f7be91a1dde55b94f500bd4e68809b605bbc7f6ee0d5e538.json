{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\components\\\\DefectMap.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';\nimport axios from 'axios';\nimport 'leaflet/dist/leaflet.css';\nimport L from 'leaflet';\nimport { Card, Row, Col, Form, Button } from 'react-bootstrap';\nimport { Link } from 'react-router-dom';\n\n// Fix for the Leaflet default icon issue\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png'\n});\n\n// Custom marker icons for different defect types using location icons\nconst createCustomIcon = (color, isVideo = false) => {\n  const iconSize = isVideo ? [36, 36] : [32, 32];\n\n  // Create different SVG content for video vs image markers\n  const svgContent = isVideo ?\n  // Video marker with camera icon made from SVG shapes (no Unicode)\n  `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"${color}\" width=\"${iconSize[0]}px\" height=\"${iconSize[1]}px\">\n      <path d=\"M0 0h24v24H0z\" fill=\"none\"/>\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\n      <rect x=\"8\" y=\"6\" width=\"8\" height=\"6\" rx=\"1\" fill=\"white\"/>\n      <circle cx=\"9.5\" cy=\"7.5\" r=\"1\" fill=\"${color}\"/>\n      <rect x=\"11\" y=\"7\" width=\"4\" height=\"2\" fill=\"${color}\"/>\n    </svg>` :\n  // Image marker with standard location pin (no Unicode)\n  `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"${color}\" width=\"${iconSize[0]}px\" height=\"${iconSize[1]}px\">\n      <path d=\"M0 0h24v24H0z\" fill=\"none\"/>\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\n    </svg>`;\n  return L.icon({\n    iconUrl: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(svgContent)}`,\n    iconSize: iconSize,\n    iconAnchor: [iconSize[0] / 2, iconSize[1]],\n    popupAnchor: [0, -iconSize[1]]\n  });\n};\nconst icons = {\n  pothole: createCustomIcon('#FF0000'),\n  // Red for potholes\n  crack: createCustomIcon('#FFCC00'),\n  // Yellow for cracks (alligator cracks)\n  kerb: createCustomIcon('#0066FF'),\n  // Blue for kerb defects\n  // Video variants\n  'pothole-video': createCustomIcon('#FF0000', true),\n  // Red for pothole videos\n  'crack-video': createCustomIcon('#FFCC00', true),\n  // Yellow for crack videos\n  'kerb-video': createCustomIcon('#0066FF', true) // Blue for kerb videos\n};\nfunction DefectMap({\n  user\n}) {\n  _s();\n  const [defects, setDefects] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [center] = useState([20.5937, 78.9629]); // India center\n  const [zoom] = useState(6); // Country-wide zoom for India\n\n  // Filters for the map\n  const [startDate, setStartDate] = useState('');\n  const [endDate, setEndDate] = useState('');\n  const [selectedUser, setSelectedUser] = useState('');\n  const [usersList, setUsersList] = useState([]);\n  const [defectTypeFilters, setDefectTypeFilters] = useState({\n    pothole: true,\n    crack: true,\n    kerb: true\n  });\n\n  // Fetch all images with defects and coordinates\n  const fetchDefectData = async (forceRefresh = false) => {\n    try {\n      setLoading(true);\n      setError(null); // Clear any previous errors\n\n      // Prepare query parameters\n      let params = {};\n      if (startDate) params.start_date = startDate;\n      if (endDate) params.end_date = endDate;\n      if (selectedUser) params.username = selectedUser;\n      if (user !== null && user !== void 0 && user.role) params.user_role = user.role;\n\n      // Add cache-busting parameter for force refresh\n      if (forceRefresh) {\n        params._t = Date.now();\n      }\n      console.log('Fetching defect data with params:', params);\n      const response = await axios.get('/api/dashboard/image-stats', {\n        params\n      });\n      console.log('API response:', response.data);\n      if (response.data.success) {\n        // Process the data to extract defect locations with type information\n        const processedDefects = [];\n        response.data.images.forEach(image => {\n          try {\n            // Only process images with valid coordinates\n            if (image.coordinates && image.coordinates !== 'Not Available') {\n              var _image$exif_data;\n              let lat, lng;\n\n              // First, try to use EXIF GPS coordinates if available (most accurate)\n              if ((_image$exif_data = image.exif_data) !== null && _image$exif_data !== void 0 && _image$exif_data.gps_coordinates) {\n                lat = image.exif_data.gps_coordinates.latitude;\n                lng = image.exif_data.gps_coordinates.longitude;\n                console.log(`🎯 Using EXIF GPS coordinates for ${image.image_id}: [${lat}, ${lng}]`);\n              } else {\n                // Fallback to stored coordinates\n                // Handle different coordinate formats\n                if (typeof image.coordinates === 'string') {\n                  // Parse coordinates (expected format: \"lat,lng\")\n                  const coords = image.coordinates.split(',');\n                  if (coords.length === 2) {\n                    lat = parseFloat(coords[0].trim());\n                    lng = parseFloat(coords[1].trim());\n                  }\n                } else if (Array.isArray(image.coordinates) && image.coordinates.length === 2) {\n                  // Handle array format [lat, lng]\n                  lat = parseFloat(image.coordinates[0]);\n                  lng = parseFloat(image.coordinates[1]);\n                } else if (typeof image.coordinates === 'object' && image.coordinates.lat && image.coordinates.lng) {\n                  // Handle object format {lat: x, lng: y}\n                  lat = parseFloat(image.coordinates.lat);\n                  lng = parseFloat(image.coordinates.lng);\n                }\n                console.log(`📍 Using stored coordinates for ${image.image_id}: [${lat}, ${lng}]`);\n              }\n\n              // Validate coordinates are within reasonable bounds\n              if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {\n                console.log(`✅ Valid coordinates for image ${image.id}: [${lat}, ${lng}]`);\n                processedDefects.push({\n                  id: image.id,\n                  image_id: image.image_id,\n                  type: image.type,\n                  position: [lat, lng],\n                  defect_count: image.defect_count,\n                  timestamp: new Date(image.timestamp).toLocaleString(),\n                  username: image.username,\n                  original_image_id: image.original_image_id,\n                  // For cracks, include type information if available\n                  type_counts: image.type_counts,\n                  // For kerbs, include condition information if available\n                  condition_counts: image.condition_counts,\n                  // EXIF and metadata information\n                  exif_data: image.exif_data || {},\n                  metadata: image.metadata || {},\n                  media_type: image.media_type || 'image',\n                  original_image_full_url: image.original_image_full_url\n                });\n              } else {\n                console.warn(`❌ Invalid coordinates for image ${image.id}:`, image.coordinates, `parsed: lat=${lat}, lng=${lng}`);\n              }\n            } else {\n              console.log(`⚠️ Skipping image ${image.id}: coordinates=${image.coordinates}`);\n            }\n          } catch (coordError) {\n            console.error(`❌ Error processing coordinates for image ${image.id}:`, coordError, image.coordinates);\n          }\n        });\n        console.log('Processed defects:', processedDefects.length);\n        setDefects(processedDefects);\n\n        // If no defects were processed, show a helpful message\n        if (processedDefects.length === 0) {\n          setError('No defects found with valid coordinates for the selected date range');\n        }\n      } else {\n        console.error('API returned success: false', response.data);\n        setError('Error fetching defect data: ' + (response.data.message || 'Unknown error'));\n      }\n      setLoading(false);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('Error fetching defect data:', err);\n      setError('Failed to load defect data: ' + (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || err.message));\n      setLoading(false);\n    }\n  };\n\n  // Fetch the list of users for the filter dropdown\n  const fetchUsers = async () => {\n    try {\n      const params = {};\n      if (user !== null && user !== void 0 && user.role) params.user_role = user.role;\n      const response = await axios.get('/api/users/all', {\n        params\n      });\n      if (response.data.success) {\n        setUsersList(response.data.users);\n      }\n    } catch (error) {\n      console.error('Error fetching users:', error);\n    }\n  };\n\n  // Initialize default dates for the last 30 days\n  useEffect(() => {\n    const currentDate = new Date();\n    const thirtyDaysAgo = new Date();\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n    setEndDate(currentDate.toISOString().split('T')[0]);\n    setStartDate(thirtyDaysAgo.toISOString().split('T')[0]);\n    console.log('DefectMap component initialized');\n  }, []);\n\n  // Fetch data when component mounts and when filters change\n  useEffect(() => {\n    fetchDefectData();\n    fetchUsers();\n  }, [user]);\n\n  // Auto-refresh every 30 seconds to catch new uploads\n  useEffect(() => {\n    const interval = setInterval(() => {\n      fetchDefectData(true); // Force refresh to get latest data\n    }, 30000); // 30 seconds\n\n    return () => clearInterval(interval);\n  }, [startDate, endDate, selectedUser, user === null || user === void 0 ? void 0 : user.role]);\n\n  // Manual refresh function\n  const handleRefresh = () => {\n    fetchDefectData(true);\n  };\n\n  // Handle filter application\n  const handleApplyFilters = () => {\n    fetchDefectData();\n  };\n\n  // Handle resetting filters\n  const handleResetFilters = () => {\n    // Reset date filters to last 30 days\n    const currentDate = new Date();\n    const thirtyDaysAgo = new Date();\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n    setEndDate(currentDate.toISOString().split('T')[0]);\n    setStartDate(thirtyDaysAgo.toISOString().split('T')[0]);\n    setSelectedUser('');\n    setDefectTypeFilters({\n      pothole: true,\n      crack: true,\n      kerb: true\n    });\n\n    // Refetch data with reset filters\n    fetchDefectData();\n  };\n\n  // Handle defect type filter changes\n  const handleDefectTypeFilterChange = type => {\n    setDefectTypeFilters(prevFilters => ({\n      ...prevFilters,\n      [type]: !prevFilters[type]\n    }));\n  };\n\n  // Filter defects based on applied filters\n  const filteredDefects = defects.filter(defect => defectTypeFilters[defect.type]);\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: \"shadow-sm dashboard-card mb-4\",\n    children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n      className: \"bg-primary text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"h5\", {\n        className: \"mb-0\",\n        children: \"Defect Map View\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        className: \"mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          lg: 4,\n          className: \"filter-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"Date Filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-controls\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-field-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-field\",\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    className: \"small\",\n                    children: \"Start Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"date\",\n                    value: startDate,\n                    onChange: e => setStartDate(e.target.value),\n                    size: \"sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 286,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-field\",\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    className: \"small\",\n                    children: \"End Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 296,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"date\",\n                    value: endDate,\n                    onChange: e => setEndDate(e.target.value),\n                    size: \"sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 297,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          lg: 4,\n          className: \"filter-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"User Filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-controls\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-field-container\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-field\",\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    className: \"small\",\n                    children: \"Select User\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                    value: selectedUser,\n                    onChange: e => setSelectedUser(e.target.value),\n                    size: \"sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"All Users\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 320,\n                      columnNumber: 23\n                    }, this), usersList.map((user, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: user.username,\n                      children: [user.username, \" (\", user.role, \")\"]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 322,\n                      columnNumber: 25\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          lg: 4,\n          className: \"filter-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"Defect Type Filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-controls defect-type-filters\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"defect-type-filter-options\",\n              style: {\n                marginTop: \"0\",\n                paddingLeft: \"0\",\n                width: \"100%\",\n                minWidth: \"150px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(Form.Check, {\n                type: \"checkbox\",\n                id: \"pothole-filter\",\n                label: \"Potholes\",\n                checked: defectTypeFilters.pothole,\n                onChange: () => handleDefectTypeFilterChange('pothole'),\n                className: \"mb-2 filter-checkbox\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                type: \"checkbox\",\n                id: \"crack-filter\",\n                label: \"Cracks\",\n                checked: defectTypeFilters.crack,\n                onChange: () => handleDefectTypeFilterChange('crack'),\n                className: \"mb-2 filter-checkbox\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                type: \"checkbox\",\n                id: \"kerb-filter\",\n                label: \"Kerbs\",\n                checked: defectTypeFilters.kerb,\n                onChange: () => handleDefectTypeFilterChange('kerb'),\n                className: \"mb-2 filter-checkbox\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        className: \"mb-3\",\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-actions-container\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              size: \"sm\",\n              onClick: handleApplyFilters,\n              className: \"me-3 filter-btn\",\n              children: \"Apply Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-secondary\",\n              size: \"sm\",\n              onClick: handleResetFilters,\n              className: \"me-3 filter-btn\",\n              children: \"Reset Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"success\",\n              size: \"sm\",\n              onClick: handleRefresh,\n              disabled: loading,\n              className: \"filter-btn\",\n              children: loading ? '🔄 Refreshing...' : '🔄 Refresh Map'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"map-legend mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"legend-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"legend-title\",\n                children: \"\\uD83D\\uDCF7 Images\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker\",\n                  style: {\n                    backgroundColor: '#FF0000'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Potholes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker\",\n                  style: {\n                    backgroundColor: '#FFCC00'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Cracks\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker\",\n                  style: {\n                    backgroundColor: '#0066FF'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Kerb Defects\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"legend-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"legend-title\",\n                children: \"\\uD83D\\uDCF9 Videos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker video-marker\",\n                  style: {\n                    backgroundColor: '#FF0000'\n                  },\n                  children: \"\\uD83D\\uDCF9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Pothole Videos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker video-marker\",\n                  style: {\n                    backgroundColor: '#FFCC00'\n                  },\n                  children: \"\\uD83D\\uDCF9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Crack Videos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"legend-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"legend-marker video-marker\",\n                  style: {\n                    backgroundColor: '#0066FF'\n                  },\n                  children: \"\\uD83D\\uDCF9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Kerb Videos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center p-5\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border text-primary\",\n          role: \"status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 435,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-danger\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 441,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"map-container\",\n        style: {\n          height: '500px',\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(MapContainer, {\n          center: center,\n          zoom: zoom,\n          style: {\n            height: '100%',\n            width: '100%'\n          },\n          scrollWheelZoom: true,\n          children: [/*#__PURE__*/_jsxDEV(TileLayer, {\n            attribution: \"\\xA9 <a href=\\\"https://www.openstreetmap.org/copyright\\\">OpenStreetMap</a> contributors\",\n            url: \"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 15\n          }, this), filteredDefects.map(defect => {\n            var _defect$exif_data, _defect$exif_data$gps, _defect$exif_data$gps2, _defect$exif_data2, _defect$exif_data3, _defect$exif_data4, _defect$metadata, _defect$metadata$form, _defect$metadata2, _defect$metadata2$bas, _defect$metadata3, _defect$metadata3$bas, _defect$metadata4, _defect$metadata4$for;\n            // Determine icon based on media type and defect type\n            const iconKey = defect.media_type === 'video' ? `${defect.type}-video` : defect.type;\n            const selectedIcon = icons[iconKey] || icons[defect.type];\n            return /*#__PURE__*/_jsxDEV(Marker, {\n              position: defect.position,\n              icon: selectedIcon,\n              children: /*#__PURE__*/_jsxDEV(Popup, {\n                maxWidth: 400,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"defect-popup\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    children: [defect.type.charAt(0).toUpperCase() + defect.type.slice(1), \" Defect\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 468,\n                    columnNumber: 23\n                  }, this), defect.media_type === 'video' && defect.representative_frame && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-3 text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: `data:image/jpeg;base64,${defect.representative_frame}`,\n                      alt: \"Video thumbnail\",\n                      className: \"img-fluid border rounded\",\n                      style: {\n                        maxHeight: '150px',\n                        maxWidth: '100%'\n                      },\n                      onError: e => {\n                        console.warn(`Failed to load representative frame for video ${defect.image_id}`);\n                        e.target.style.display = 'none';\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 473,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                        className: \"text-info fw-bold\",\n                        children: \"\\uD83D\\uDCF9 Video Representative Frame\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 484,\n                        columnNumber: 29\n                      }, this), defect.video_id && /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: /*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-muted d-block\",\n                          children: [\"Video ID: \", defect.video_id]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 487,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 486,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 483,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 472,\n                    columnNumber: 25\n                  }, this), defect.media_type !== 'video' && defect.original_image_full_url && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-3 text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: defect.original_image_full_url,\n                      alt: \"Defect image\",\n                      className: \"img-fluid border rounded\",\n                      style: {\n                        maxHeight: '150px',\n                        maxWidth: '100%'\n                      },\n                      onError: e => {\n                        console.warn(`Failed to load image for defect ${defect.image_id}`);\n                        e.target.style.display = 'none';\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 497,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"small\", {\n                        className: \"text-primary fw-bold\",\n                        children: \"\\uD83D\\uDCF7 Original Image\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 508,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 507,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 496,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"text-primary\",\n                      children: \"Basic Information\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 515,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                      className: \"list-unstyled small\",\n                      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Count:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 517,\n                          columnNumber: 31\n                        }, this), \" \", defect.defect_count]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 517,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Date:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 518,\n                          columnNumber: 31\n                        }, this), \" \", defect.timestamp]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 518,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Reported by:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 519,\n                          columnNumber: 31\n                        }, this), \" \", defect.username]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 519,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Media Type:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 520,\n                          columnNumber: 31\n                        }, this), \" \", defect.media_type]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 520,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"GPS:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 521,\n                          columnNumber: 31\n                        }, this), \" \", defect.position[0].toFixed(6), \", \", defect.position[1].toFixed(6)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 521,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 516,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 514,\n                    columnNumber: 23\n                  }, this), defect.type === 'crack' && defect.type_counts && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"text-primary\",\n                      children: \"Crack Types\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 528,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                      className: \"list-unstyled small\",\n                      children: Object.entries(defect.type_counts).map(([type, count]) => /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: [type, \":\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 531,\n                          columnNumber: 46\n                        }, this), \" \", count]\n                      }, type, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 531,\n                        columnNumber: 31\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 529,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 527,\n                    columnNumber: 25\n                  }, this), defect.type === 'kerb' && defect.condition_counts && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"text-primary\",\n                      children: \"Kerb Conditions\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 539,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                      className: \"list-unstyled small\",\n                      children: Object.entries(defect.condition_counts).map(([condition, count]) => /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: [condition, \":\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 542,\n                          columnNumber: 51\n                        }, this), \" \", count]\n                      }, condition, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 542,\n                        columnNumber: 31\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 540,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 538,\n                    columnNumber: 25\n                  }, this), (defect.exif_data || defect.metadata) && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"text-primary\",\n                      children: \"\\uD83D\\uDCCA Media Information\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 551,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"small\",\n                      children: [((_defect$exif_data = defect.exif_data) === null || _defect$exif_data === void 0 ? void 0 : _defect$exif_data.gps_coordinates) && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-2 p-2 bg-light rounded\",\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          className: \"text-success\",\n                          children: \"\\uD83C\\uDF0D GPS (EXIF):\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 556,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [\"Lat: \", (_defect$exif_data$gps = defect.exif_data.gps_coordinates.latitude) === null || _defect$exif_data$gps === void 0 ? void 0 : _defect$exif_data$gps.toFixed(6)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 557,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [\"Lng: \", (_defect$exif_data$gps2 = defect.exif_data.gps_coordinates.longitude) === null || _defect$exif_data$gps2 === void 0 ? void 0 : _defect$exif_data$gps2.toFixed(6)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 558,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 555,\n                        columnNumber: 31\n                      }, this), ((_defect$exif_data2 = defect.exif_data) === null || _defect$exif_data2 === void 0 ? void 0 : _defect$exif_data2.camera_info) && Object.keys(defect.exif_data.camera_info).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"\\uD83D\\uDCF7 Camera:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 565,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                          className: \"list-unstyled ms-2\",\n                          children: [defect.exif_data.camera_info.camera_make && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [\"Make: \", defect.exif_data.camera_info.camera_make]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 568,\n                            columnNumber: 37\n                          }, this), defect.exif_data.camera_info.camera_model && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [\"Model: \", defect.exif_data.camera_info.camera_model]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 571,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 566,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 564,\n                        columnNumber: 31\n                      }, this), ((_defect$exif_data3 = defect.exif_data) === null || _defect$exif_data3 === void 0 ? void 0 : _defect$exif_data3.technical_info) && Object.keys(defect.exif_data.technical_info).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"\\u2699\\uFE0F Technical:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 580,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                          className: \"list-unstyled ms-2\",\n                          children: [defect.exif_data.technical_info.iso && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [\"ISO: \", defect.exif_data.technical_info.iso]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 583,\n                            columnNumber: 37\n                          }, this), defect.exif_data.technical_info.exposure_time && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [\"Exposure: \", defect.exif_data.technical_info.exposure_time]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 586,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 581,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 579,\n                        columnNumber: 31\n                      }, this), ((_defect$exif_data4 = defect.exif_data) === null || _defect$exif_data4 === void 0 ? void 0 : _defect$exif_data4.basic_info) && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"\\uD83D\\uDCD0 Dimensions:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 595,\n                          columnNumber: 33\n                        }, this), \" \", defect.exif_data.basic_info.width, \" \\xD7 \", defect.exif_data.basic_info.height, defect.exif_data.basic_info.format && /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: [\" (\", defect.exif_data.basic_info.format, \")\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 597,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 594,\n                        columnNumber: 31\n                      }, this), defect.media_type === 'video' && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"text-info\",\n                          children: \"\\uD83D\\uDCF9 Video Information\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 605,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                          className: \"list-unstyled small\",\n                          children: [((_defect$metadata = defect.metadata) === null || _defect$metadata === void 0 ? void 0 : (_defect$metadata$form = _defect$metadata.format_info) === null || _defect$metadata$form === void 0 ? void 0 : _defect$metadata$form.duration) && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"Duration:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 608,\n                              columnNumber: 41\n                            }, this), \" \", Math.round(defect.metadata.format_info.duration), \"s\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 608,\n                            columnNumber: 37\n                          }, this), ((_defect$metadata2 = defect.metadata) === null || _defect$metadata2 === void 0 ? void 0 : (_defect$metadata2$bas = _defect$metadata2.basic_info) === null || _defect$metadata2$bas === void 0 ? void 0 : _defect$metadata2$bas.width) && ((_defect$metadata3 = defect.metadata) === null || _defect$metadata3 === void 0 ? void 0 : (_defect$metadata3$bas = _defect$metadata3.basic_info) === null || _defect$metadata3$bas === void 0 ? void 0 : _defect$metadata3$bas.height) && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"Resolution:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 611,\n                              columnNumber: 41\n                            }, this), \" \", defect.metadata.basic_info.width, \"x\", defect.metadata.basic_info.height]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 611,\n                            columnNumber: 37\n                          }, this), ((_defect$metadata4 = defect.metadata) === null || _defect$metadata4 === void 0 ? void 0 : (_defect$metadata4$for = _defect$metadata4.format_info) === null || _defect$metadata4$for === void 0 ? void 0 : _defect$metadata4$for.format_name) && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"Format:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 614,\n                              columnNumber: 41\n                            }, this), \" \", defect.metadata.format_info.format_name.toUpperCase()]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 614,\n                            columnNumber: 37\n                          }, this), defect.video_id && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"Video ID:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 617,\n                              columnNumber: 41\n                            }, this), \" \", defect.video_id]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 617,\n                            columnNumber: 37\n                          }, this), defect.original_video_url && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"Original Video:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 620,\n                              columnNumber: 41\n                            }, this), \" Available\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 620,\n                            columnNumber: 37\n                          }, this), defect.processed_video_url && /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"Processed Video:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 623,\n                              columnNumber: 41\n                            }, this), \" Available\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 623,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 606,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 604,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 552,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 550,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(Link, {\n                      to: `/view/${defect.image_id}`,\n                      className: \"btn btn-sm btn-primary\",\n                      onClick: e => e.stopPropagation(),\n                      children: \"View Details\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 634,\n                      columnNumber: 25\n                    }, this), defect.original_image_full_url && /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: defect.original_image_full_url,\n                      target: \"_blank\",\n                      rel: \"noopener noreferrer\",\n                      className: \"btn btn-sm btn-outline-secondary\",\n                      onClick: e => e.stopPropagation(),\n                      children: \"View Original\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 642,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 633,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 19\n              }, this)\n            }, defect.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 17\n            }, this);\n          })]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 443,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 273,\n    columnNumber: 5\n  }, this);\n}\n_s(DefectMap, \"sRYxfvG8F3dXQVEP4Qy79z2muvg=\");\n_c = DefectMap;\nexport default DefectMap;\nvar _c;\n$RefreshReg$(_c, \"DefectMap\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Popup", "axios", "L", "Card", "Row", "Col", "Form", "<PERSON><PERSON>", "Link", "jsxDEV", "_jsxDEV", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "iconUrl", "shadowUrl", "createCustomIcon", "color", "isVideo", "iconSize", "svgContent", "icon", "encodeURIComponent", "iconAnchor", "popupAnchor", "icons", "pothole", "crack", "kerb", "DefectMap", "user", "_s", "defects", "setDefects", "loading", "setLoading", "error", "setError", "center", "zoom", "startDate", "setStartDate", "endDate", "setEndDate", "selected<PERSON>ser", "setSelectedUser", "usersList", "setUsersList", "defectTypeFilters", "setDefectTypeFilters", "fetchDefectData", "forceRefresh", "params", "start_date", "end_date", "username", "role", "user_role", "_t", "Date", "now", "console", "log", "response", "get", "data", "success", "processedDefects", "images", "for<PERSON>ach", "image", "coordinates", "_image$exif_data", "lat", "lng", "exif_data", "gps_coordinates", "latitude", "longitude", "image_id", "coords", "split", "length", "parseFloat", "trim", "Array", "isArray", "isNaN", "id", "push", "type", "position", "defect_count", "timestamp", "toLocaleString", "original_image_id", "type_counts", "condition_counts", "metadata", "media_type", "original_image_full_url", "warn", "coordError", "message", "err", "_err$response", "_err$response$data", "fetchUsers", "users", "currentDate", "thirtyDaysAgo", "setDate", "getDate", "toISOString", "interval", "setInterval", "clearInterval", "handleRefresh", "handleApplyFilters", "handleResetFilters", "handleDefectTypeFilterChange", "prevFilters", "filteredDefects", "filter", "defect", "className", "children", "Header", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "lg", "Group", "Label", "Control", "value", "onChange", "e", "target", "size", "Select", "map", "index", "style", "marginTop", "paddingLeft", "width", "min<PERSON><PERSON><PERSON>", "Check", "label", "checked", "variant", "onClick", "disabled", "backgroundColor", "height", "scrollWheelZoom", "attribution", "url", "_defect$exif_data", "_defect$exif_data$gps", "_defect$exif_data$gps2", "_defect$exif_data2", "_defect$exif_data3", "_defect$exif_data4", "_defect$metadata", "_defect$metadata$form", "_defect$metadata2", "_defect$metadata2$bas", "_defect$metadata3", "_defect$metadata3$bas", "_defect$metadata4", "_defect$metadata4$for", "<PERSON><PERSON><PERSON>", "selectedIcon", "max<PERSON><PERSON><PERSON>", "char<PERSON>t", "toUpperCase", "slice", "representative_frame", "src", "alt", "maxHeight", "onError", "display", "video_id", "toFixed", "Object", "entries", "count", "condition", "camera_info", "keys", "camera_make", "camera_model", "technical_info", "iso", "exposure_time", "basic_info", "format", "format_info", "duration", "Math", "round", "format_name", "original_video_url", "processed_video_url", "to", "stopPropagation", "href", "rel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/components/DefectMap.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';\r\nimport axios from 'axios';\r\nimport 'leaflet/dist/leaflet.css';\r\nimport L from 'leaflet';\r\nimport { Card, Row, Col, Form, Button } from 'react-bootstrap';\r\nimport { Link } from 'react-router-dom';\r\n\r\n// Fix for the Leaflet default icon issue\r\ndelete L.Icon.Default.prototype._getIconUrl;\r\nL.Icon.Default.mergeOptions({\r\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\r\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\r\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',\r\n});\r\n\r\n// Custom marker icons for different defect types using location icons\r\nconst createCustomIcon = (color, isVideo = false) => {\r\n  const iconSize = isVideo ? [36, 36] : [32, 32];\r\n\r\n  // Create different SVG content for video vs image markers\r\n  const svgContent = isVideo ?\r\n    // Video marker with camera icon made from SVG shapes (no Unicode)\r\n    `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"${color}\" width=\"${iconSize[0]}px\" height=\"${iconSize[1]}px\">\r\n      <path d=\"M0 0h24v24H0z\" fill=\"none\"/>\r\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\r\n      <rect x=\"8\" y=\"6\" width=\"8\" height=\"6\" rx=\"1\" fill=\"white\"/>\r\n      <circle cx=\"9.5\" cy=\"7.5\" r=\"1\" fill=\"${color}\"/>\r\n      <rect x=\"11\" y=\"7\" width=\"4\" height=\"2\" fill=\"${color}\"/>\r\n    </svg>` :\r\n    // Image marker with standard location pin (no Unicode)\r\n    `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"${color}\" width=\"${iconSize[0]}px\" height=\"${iconSize[1]}px\">\r\n      <path d=\"M0 0h24v24H0z\" fill=\"none\"/>\r\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\r\n    </svg>`;\r\n\r\n  return L.icon({\r\n    iconUrl: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(svgContent)}`,\r\n    iconSize: iconSize,\r\n    iconAnchor: [iconSize[0]/2, iconSize[1]],\r\n    popupAnchor: [0, -iconSize[1]],\r\n  });\r\n};\r\n\r\nconst icons = {\r\n  pothole: createCustomIcon('#FF0000'), // Red for potholes\r\n  crack: createCustomIcon('#FFCC00'),   // Yellow for cracks (alligator cracks)\r\n  kerb: createCustomIcon('#0066FF'),    // Blue for kerb defects\r\n  // Video variants\r\n  'pothole-video': createCustomIcon('#FF0000', true), // Red for pothole videos\r\n  'crack-video': createCustomIcon('#FFCC00', true),   // Yellow for crack videos\r\n  'kerb-video': createCustomIcon('#0066FF', true),    // Blue for kerb videos\r\n};\r\n\r\nfunction DefectMap({ user }) {\r\n  const [defects, setDefects] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [center] = useState([20.5937, 78.9629]); // India center\r\n  const [zoom] = useState(6); // Country-wide zoom for India\r\n  \r\n  // Filters for the map\r\n  const [startDate, setStartDate] = useState('');\r\n  const [endDate, setEndDate] = useState('');\r\n  const [selectedUser, setSelectedUser] = useState('');\r\n  const [usersList, setUsersList] = useState([]);\r\n  const [defectTypeFilters, setDefectTypeFilters] = useState({\r\n    pothole: true,\r\n    crack: true,\r\n    kerb: true,\r\n  });\r\n\r\n  // Fetch all images with defects and coordinates\r\n  const fetchDefectData = async (forceRefresh = false) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null); // Clear any previous errors\r\n\r\n      // Prepare query parameters\r\n      let params = {};\r\n      if (startDate) params.start_date = startDate;\r\n      if (endDate) params.end_date = endDate;\r\n      if (selectedUser) params.username = selectedUser;\r\n      if (user?.role) params.user_role = user.role;\r\n\r\n      // Add cache-busting parameter for force refresh\r\n      if (forceRefresh) {\r\n        params._t = Date.now();\r\n      }\r\n\r\n      console.log('Fetching defect data with params:', params);\r\n      const response = await axios.get('/api/dashboard/image-stats', { params });\r\n      console.log('API response:', response.data);\r\n\r\n      if (response.data.success) {\r\n        // Process the data to extract defect locations with type information\r\n        const processedDefects = [];\r\n        \r\n        response.data.images.forEach(image => {\r\n          try {\r\n            // Only process images with valid coordinates\r\n            if (image.coordinates && image.coordinates !== 'Not Available') {\r\n              let lat, lng;\r\n\r\n              // First, try to use EXIF GPS coordinates if available (most accurate)\r\n              if (image.exif_data?.gps_coordinates) {\r\n                lat = image.exif_data.gps_coordinates.latitude;\r\n                lng = image.exif_data.gps_coordinates.longitude;\r\n                console.log(`🎯 Using EXIF GPS coordinates for ${image.image_id}: [${lat}, ${lng}]`);\r\n              } else {\r\n                // Fallback to stored coordinates\r\n                // Handle different coordinate formats\r\n                if (typeof image.coordinates === 'string') {\r\n                  // Parse coordinates (expected format: \"lat,lng\")\r\n                  const coords = image.coordinates.split(',');\r\n                  if (coords.length === 2) {\r\n                    lat = parseFloat(coords[0].trim());\r\n                    lng = parseFloat(coords[1].trim());\r\n                  }\r\n                } else if (Array.isArray(image.coordinates) && image.coordinates.length === 2) {\r\n                  // Handle array format [lat, lng]\r\n                  lat = parseFloat(image.coordinates[0]);\r\n                  lng = parseFloat(image.coordinates[1]);\r\n                } else if (typeof image.coordinates === 'object' && image.coordinates.lat && image.coordinates.lng) {\r\n                  // Handle object format {lat: x, lng: y}\r\n                  lat = parseFloat(image.coordinates.lat);\r\n                  lng = parseFloat(image.coordinates.lng);\r\n                }\r\n                console.log(`📍 Using stored coordinates for ${image.image_id}: [${lat}, ${lng}]`);\r\n              }\r\n\r\n            // Validate coordinates are within reasonable bounds\r\n            if (!isNaN(lat) && !isNaN(lng) &&\r\n                lat >= -90 && lat <= 90 &&\r\n                lng >= -180 && lng <= 180) {\r\n              console.log(`✅ Valid coordinates for image ${image.id}: [${lat}, ${lng}]`);\r\n              processedDefects.push({\r\n                id: image.id,\r\n                image_id: image.image_id,\r\n                type: image.type,\r\n                position: [lat, lng],\r\n                defect_count: image.defect_count,\r\n                timestamp: new Date(image.timestamp).toLocaleString(),\r\n                username: image.username,\r\n                original_image_id: image.original_image_id,\r\n                // For cracks, include type information if available\r\n                type_counts: image.type_counts,\r\n                // For kerbs, include condition information if available\r\n                condition_counts: image.condition_counts,\r\n                // EXIF and metadata information\r\n                exif_data: image.exif_data || {},\r\n                metadata: image.metadata || {},\r\n                media_type: image.media_type || 'image',\r\n                original_image_full_url: image.original_image_full_url\r\n              });\r\n            } else {\r\n              console.warn(`❌ Invalid coordinates for image ${image.id}:`, image.coordinates, `parsed: lat=${lat}, lng=${lng}`);\r\n            }\r\n          } else {\r\n            console.log(`⚠️ Skipping image ${image.id}: coordinates=${image.coordinates}`);\r\n          }\r\n          } catch (coordError) {\r\n            console.error(`❌ Error processing coordinates for image ${image.id}:`, coordError, image.coordinates);\r\n          }\r\n        });\r\n\r\n        console.log('Processed defects:', processedDefects.length);\r\n        setDefects(processedDefects);\r\n\r\n        // If no defects were processed, show a helpful message\r\n        if (processedDefects.length === 0) {\r\n          setError('No defects found with valid coordinates for the selected date range');\r\n        }\r\n      } else {\r\n        console.error('API returned success: false', response.data);\r\n        setError('Error fetching defect data: ' + (response.data.message || 'Unknown error'));\r\n      }\r\n\r\n      setLoading(false);\r\n    } catch (err) {\r\n      console.error('Error fetching defect data:', err);\r\n      setError('Failed to load defect data: ' + (err.response?.data?.message || err.message));\r\n      setLoading(false);\r\n    }\r\n  };\r\n  \r\n  // Fetch the list of users for the filter dropdown\r\n  const fetchUsers = async () => {\r\n    try {\r\n      const params = {};\r\n      if (user?.role) params.user_role = user.role;\r\n      \r\n      const response = await axios.get('/api/users/all', { params });\r\n      if (response.data.success) {\r\n        setUsersList(response.data.users);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching users:', error);\r\n    }\r\n  };\r\n\r\n  // Initialize default dates for the last 30 days\r\n  useEffect(() => {\r\n    const currentDate = new Date();\r\n    const thirtyDaysAgo = new Date();\r\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\r\n\r\n    setEndDate(currentDate.toISOString().split('T')[0]);\r\n    setStartDate(thirtyDaysAgo.toISOString().split('T')[0]);\r\n\r\n    console.log('DefectMap component initialized');\r\n  }, []);\r\n\r\n  // Fetch data when component mounts and when filters change\r\n  useEffect(() => {\r\n    fetchDefectData();\r\n    fetchUsers();\r\n  }, [user]);\r\n\r\n  // Auto-refresh every 30 seconds to catch new uploads\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      fetchDefectData(true); // Force refresh to get latest data\r\n    }, 30000); // 30 seconds\r\n\r\n    return () => clearInterval(interval);\r\n  }, [startDate, endDate, selectedUser, user?.role]);\r\n\r\n  // Manual refresh function\r\n  const handleRefresh = () => {\r\n    fetchDefectData(true);\r\n  };\r\n\r\n  // Handle filter application\r\n  const handleApplyFilters = () => {\r\n    fetchDefectData();\r\n  };\r\n\r\n  // Handle resetting filters\r\n  const handleResetFilters = () => {\r\n    // Reset date filters to last 30 days\r\n    const currentDate = new Date();\r\n    const thirtyDaysAgo = new Date();\r\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\r\n    \r\n    setEndDate(currentDate.toISOString().split('T')[0]);\r\n    setStartDate(thirtyDaysAgo.toISOString().split('T')[0]);\r\n    setSelectedUser('');\r\n    setDefectTypeFilters({\r\n      pothole: true,\r\n      crack: true,\r\n      kerb: true,\r\n    });\r\n    \r\n    // Refetch data with reset filters\r\n    fetchDefectData();\r\n  };\r\n\r\n  // Handle defect type filter changes\r\n  const handleDefectTypeFilterChange = (type) => {\r\n    setDefectTypeFilters(prevFilters => ({\r\n      ...prevFilters,\r\n      [type]: !prevFilters[type]\r\n    }));\r\n  };\r\n\r\n  // Filter defects based on applied filters\r\n  const filteredDefects = defects.filter(defect => \r\n    defectTypeFilters[defect.type]\r\n  );\r\n\r\n  return (\r\n    <Card className=\"shadow-sm dashboard-card mb-4\">\r\n      <Card.Header className=\"bg-primary text-white\">\r\n        <h5 className=\"mb-0\">Defect Map View</h5>\r\n      </Card.Header>\r\n      <Card.Body>\r\n        <Row className=\"mb-3\">\r\n          <Col lg={4} className=\"filter-section\">\r\n            <h6>Date Filter</h6>\r\n            <div className=\"filter-controls\">\r\n              <div className=\"filter-field-container\">\r\n                <div className=\"filter-field\">\r\n                  <Form.Group>\r\n                    <Form.Label className=\"small\">Start Date</Form.Label>\r\n                    <Form.Control\r\n                      type=\"date\"\r\n                      value={startDate}\r\n                      onChange={(e) => setStartDate(e.target.value)}\r\n                      size=\"sm\"\r\n                    />\r\n                  </Form.Group>\r\n                </div>\r\n                <div className=\"filter-field\">\r\n                  <Form.Group>\r\n                    <Form.Label className=\"small\">End Date</Form.Label>\r\n                    <Form.Control\r\n                      type=\"date\"\r\n                      value={endDate}\r\n                      onChange={(e) => setEndDate(e.target.value)}\r\n                      size=\"sm\"\r\n                    />\r\n                  </Form.Group>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </Col>\r\n          <Col lg={4} className=\"filter-section\">\r\n            <h6>User Filter</h6>\r\n            <div className=\"filter-controls\">\r\n              <div className=\"filter-field-container\">\r\n                <div className=\"filter-field\">\r\n                  <Form.Group>\r\n                    <Form.Label className=\"small\">Select User</Form.Label>\r\n                    <Form.Select\r\n                      value={selectedUser}\r\n                      onChange={(e) => setSelectedUser(e.target.value)}\r\n                      size=\"sm\"\r\n                    >\r\n                      <option value=\"\">All Users</option>\r\n                      {usersList.map((user, index) => (\r\n                        <option key={index} value={user.username}>\r\n                          {user.username} ({user.role})\r\n                        </option>\r\n                      ))}\r\n                    </Form.Select>\r\n                  </Form.Group>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </Col>\r\n          <Col lg={4} className=\"filter-section\">\r\n            <h6>Defect Type Filter</h6>\r\n            <div className=\"filter-controls defect-type-filters\">\r\n              <div className=\"defect-type-filter-options\" style={{ marginTop: \"0\", paddingLeft: \"0\", width: \"100%\", minWidth: \"150px\" }}>\r\n                <Form.Check\r\n                  type=\"checkbox\"\r\n                  id=\"pothole-filter\"\r\n                  label=\"Potholes\"\r\n                  checked={defectTypeFilters.pothole}\r\n                  onChange={() => handleDefectTypeFilterChange('pothole')}\r\n                  className=\"mb-2 filter-checkbox\"\r\n                />\r\n                <Form.Check\r\n                  type=\"checkbox\"\r\n                  id=\"crack-filter\"\r\n                  label=\"Cracks\"\r\n                  checked={defectTypeFilters.crack}\r\n                  onChange={() => handleDefectTypeFilterChange('crack')}\r\n                  className=\"mb-2 filter-checkbox\"\r\n                />\r\n                <Form.Check\r\n                  type=\"checkbox\"\r\n                  id=\"kerb-filter\"\r\n                  label=\"Kerbs\"\r\n                  checked={defectTypeFilters.kerb}\r\n                  onChange={() => handleDefectTypeFilterChange('kerb')}\r\n                  className=\"mb-2 filter-checkbox\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n        \r\n        <Row className=\"mb-3\">\r\n          <Col className=\"text-center\">\r\n            <div className=\"filter-actions-container\">\r\n              <Button \r\n                variant=\"primary\" \r\n                size=\"sm\" \r\n                onClick={handleApplyFilters}\r\n                className=\"me-3 filter-btn\"\r\n              >\r\n                Apply Filters\r\n              </Button>\r\n              <Button\r\n                variant=\"outline-secondary\"\r\n                size=\"sm\"\r\n                onClick={handleResetFilters}\r\n                className=\"me-3 filter-btn\"\r\n              >\r\n                Reset Filters\r\n              </Button>\r\n              <Button\r\n                variant=\"success\"\r\n                size=\"sm\"\r\n                onClick={handleRefresh}\r\n                disabled={loading}\r\n                className=\"filter-btn\"\r\n              >\r\n                {loading ? '🔄 Refreshing...' : '🔄 Refresh Map'}\r\n              </Button>\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n\r\n        <Row>\r\n          <Col>\r\n            <div className=\"map-legend mb-3\">\r\n              <div className=\"legend-section\">\r\n                <h6 className=\"legend-title\">📷 Images</h6>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker\" style={{ backgroundColor: '#FF0000' }}></div>\r\n                  <span>Potholes</span>\r\n                </div>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker\" style={{ backgroundColor: '#FFCC00' }}></div>\r\n                  <span>Cracks</span>\r\n                </div>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker\" style={{ backgroundColor: '#0066FF' }}></div>\r\n                  <span>Kerb Defects</span>\r\n                </div>\r\n              </div>\r\n              <div className=\"legend-section\">\r\n                <h6 className=\"legend-title\">📹 Videos</h6>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker video-marker\" style={{ backgroundColor: '#FF0000' }}>📹</div>\r\n                  <span>Pothole Videos</span>\r\n                </div>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker video-marker\" style={{ backgroundColor: '#FFCC00' }}>📹</div>\r\n                  <span>Crack Videos</span>\r\n                </div>\r\n                <div className=\"legend-item\">\r\n                  <div className=\"legend-marker video-marker\" style={{ backgroundColor: '#0066FF' }}>📹</div>\r\n                  <span>Kerb Videos</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n\r\n        {loading ? (\r\n          <div className=\"text-center p-5\">\r\n            <div className=\"spinner-border text-primary\" role=\"status\">\r\n              <span className=\"visually-hidden\">Loading...</span>\r\n            </div>\r\n          </div>\r\n        ) : error ? (\r\n          <div className=\"alert alert-danger\">{error}</div>\r\n        ) : (\r\n          <div className=\"map-container\" style={{ height: '500px', width: '100%' }}>\r\n            <MapContainer \r\n              center={center} \r\n              zoom={zoom} \r\n              style={{ height: '100%', width: '100%' }}\r\n              scrollWheelZoom={true}\r\n            >\r\n              <TileLayer\r\n                attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\r\n                url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\r\n              />\r\n              \r\n              {filteredDefects.map((defect) => {\r\n                // Determine icon based on media type and defect type\r\n                const iconKey = defect.media_type === 'video' ? `${defect.type}-video` : defect.type;\r\n                const selectedIcon = icons[iconKey] || icons[defect.type];\r\n\r\n                return (\r\n                <Marker\r\n                  key={defect.id}\r\n                  position={defect.position}\r\n                  icon={selectedIcon}\r\n                >\r\n                  <Popup maxWidth={400}>\r\n                    <div className=\"defect-popup\">\r\n                      <h6>{defect.type.charAt(0).toUpperCase() + defect.type.slice(1)} Defect</h6>\r\n\r\n                      {/* Video Thumbnail */}\r\n                      {defect.media_type === 'video' && defect.representative_frame && (\r\n                        <div className=\"mb-3 text-center\">\r\n                          <img\r\n                            src={`data:image/jpeg;base64,${defect.representative_frame}`}\r\n                            alt=\"Video thumbnail\"\r\n                            className=\"img-fluid border rounded\"\r\n                            style={{ maxHeight: '150px', maxWidth: '100%' }}\r\n                            onError={(e) => {\r\n                              console.warn(`Failed to load representative frame for video ${defect.image_id}`);\r\n                              e.target.style.display = 'none';\r\n                            }}\r\n                          />\r\n                          <div className=\"mt-1\">\r\n                            <small className=\"text-info fw-bold\">📹 Video Representative Frame</small>\r\n                            {defect.video_id && (\r\n                              <div>\r\n                                <small className=\"text-muted d-block\">Video ID: {defect.video_id}</small>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Regular Image Display */}\r\n                      {defect.media_type !== 'video' && defect.original_image_full_url && (\r\n                        <div className=\"mb-3 text-center\">\r\n                          <img\r\n                            src={defect.original_image_full_url}\r\n                            alt=\"Defect image\"\r\n                            className=\"img-fluid border rounded\"\r\n                            style={{ maxHeight: '150px', maxWidth: '100%' }}\r\n                            onError={(e) => {\r\n                              console.warn(`Failed to load image for defect ${defect.image_id}`);\r\n                              e.target.style.display = 'none';\r\n                            }}\r\n                          />\r\n                          <div className=\"mt-1\">\r\n                            <small className=\"text-primary fw-bold\">📷 Original Image</small>\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Basic Information */}\r\n                      <div className=\"mb-3\">\r\n                        <h6 className=\"text-primary\">Basic Information</h6>\r\n                        <ul className=\"list-unstyled small\">\r\n                          <li><strong>Count:</strong> {defect.defect_count}</li>\r\n                          <li><strong>Date:</strong> {defect.timestamp}</li>\r\n                          <li><strong>Reported by:</strong> {defect.username}</li>\r\n                          <li><strong>Media Type:</strong> {defect.media_type}</li>\r\n                          <li><strong>GPS:</strong> {defect.position[0].toFixed(6)}, {defect.position[1].toFixed(6)}</li>\r\n                        </ul>\r\n                      </div>\r\n\r\n                      {/* Defect-specific Information */}\r\n                      {defect.type === 'crack' && defect.type_counts && (\r\n                        <div className=\"mb-3\">\r\n                          <h6 className=\"text-primary\">Crack Types</h6>\r\n                          <ul className=\"list-unstyled small\">\r\n                            {Object.entries(defect.type_counts).map(([type, count]) => (\r\n                              <li key={type}><strong>{type}:</strong> {count}</li>\r\n                            ))}\r\n                          </ul>\r\n                        </div>\r\n                      )}\r\n\r\n                      {defect.type === 'kerb' && defect.condition_counts && (\r\n                        <div className=\"mb-3\">\r\n                          <h6 className=\"text-primary\">Kerb Conditions</h6>\r\n                          <ul className=\"list-unstyled small\">\r\n                            {Object.entries(defect.condition_counts).map(([condition, count]) => (\r\n                              <li key={condition}><strong>{condition}:</strong> {count}</li>\r\n                            ))}\r\n                          </ul>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* EXIF/Metadata Information */}\r\n                      {(defect.exif_data || defect.metadata) && (\r\n                        <div className=\"mb-3\">\r\n                          <h6 className=\"text-primary\">📊 Media Information</h6>\r\n                          <div className=\"small\">\r\n                            {/* GPS Information - Show first as it's most important for mapping */}\r\n                            {defect.exif_data?.gps_coordinates && (\r\n                              <div className=\"mb-2 p-2 bg-light rounded\">\r\n                                <strong className=\"text-success\">🌍 GPS (EXIF):</strong>\r\n                                <div>Lat: {defect.exif_data.gps_coordinates.latitude?.toFixed(6)}</div>\r\n                                <div>Lng: {defect.exif_data.gps_coordinates.longitude?.toFixed(6)}</div>\r\n                              </div>\r\n                            )}\r\n\r\n                            {/* Camera Information */}\r\n                            {defect.exif_data?.camera_info && Object.keys(defect.exif_data.camera_info).length > 0 && (\r\n                              <div className=\"mb-2\">\r\n                                <strong>📷 Camera:</strong>\r\n                                <ul className=\"list-unstyled ms-2\">\r\n                                  {defect.exif_data.camera_info.camera_make && (\r\n                                    <li>Make: {defect.exif_data.camera_info.camera_make}</li>\r\n                                  )}\r\n                                  {defect.exif_data.camera_info.camera_model && (\r\n                                    <li>Model: {defect.exif_data.camera_info.camera_model}</li>\r\n                                  )}\r\n                                </ul>\r\n                              </div>\r\n                            )}\r\n\r\n                            {/* Technical Information */}\r\n                            {defect.exif_data?.technical_info && Object.keys(defect.exif_data.technical_info).length > 0 && (\r\n                              <div className=\"mb-2\">\r\n                                <strong>⚙️ Technical:</strong>\r\n                                <ul className=\"list-unstyled ms-2\">\r\n                                  {defect.exif_data.technical_info.iso && (\r\n                                    <li>ISO: {defect.exif_data.technical_info.iso}</li>\r\n                                  )}\r\n                                  {defect.exif_data.technical_info.exposure_time && (\r\n                                    <li>Exposure: {defect.exif_data.technical_info.exposure_time}</li>\r\n                                  )}\r\n                                </ul>\r\n                              </div>\r\n                            )}\r\n\r\n                            {/* Basic Media Info */}\r\n                            {defect.exif_data?.basic_info && (\r\n                              <div className=\"mb-2\">\r\n                                <strong>📐 Dimensions:</strong> {defect.exif_data.basic_info.width} × {defect.exif_data.basic_info.height}\r\n                                {defect.exif_data.basic_info.format && (\r\n                                  <span> ({defect.exif_data.basic_info.format})</span>\r\n                                )}\r\n                              </div>\r\n                            )}\r\n\r\n                            {/* Video-specific metadata */}\r\n                            {defect.media_type === 'video' && (\r\n                              <div className=\"mb-3\">\r\n                                <h6 className=\"text-info\">📹 Video Information</h6>\r\n                                <ul className=\"list-unstyled small\">\r\n                                  {defect.metadata?.format_info?.duration && (\r\n                                    <li><strong>Duration:</strong> {Math.round(defect.metadata.format_info.duration)}s</li>\r\n                                  )}\r\n                                  {defect.metadata?.basic_info?.width && defect.metadata?.basic_info?.height && (\r\n                                    <li><strong>Resolution:</strong> {defect.metadata.basic_info.width}x{defect.metadata.basic_info.height}</li>\r\n                                  )}\r\n                                  {defect.metadata?.format_info?.format_name && (\r\n                                    <li><strong>Format:</strong> {defect.metadata.format_info.format_name.toUpperCase()}</li>\r\n                                  )}\r\n                                  {defect.video_id && (\r\n                                    <li><strong>Video ID:</strong> {defect.video_id}</li>\r\n                                  )}\r\n                                  {defect.original_video_url && (\r\n                                    <li><strong>Original Video:</strong> Available</li>\r\n                                  )}\r\n                                  {defect.processed_video_url && (\r\n                                    <li><strong>Processed Video:</strong> Available</li>\r\n                                  )}\r\n                                </ul>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Action Buttons */}\r\n                      <div className=\"d-flex gap-2\">\r\n                        <Link\r\n                          to={`/view/${defect.image_id}`}\r\n                          className=\"btn btn-sm btn-primary\"\r\n                          onClick={(e) => e.stopPropagation()}\r\n                        >\r\n                          View Details\r\n                        </Link>\r\n                        {defect.original_image_full_url && (\r\n                          <a\r\n                            href={defect.original_image_full_url}\r\n                            target=\"_blank\"\r\n                            rel=\"noopener noreferrer\"\r\n                            className=\"btn btn-sm btn-outline-secondary\"\r\n                            onClick={(e) => e.stopPropagation()}\r\n                          >\r\n                            View Original\r\n                          </a>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </Popup>\r\n                </Marker>\r\n                );\r\n              })}\r\n            </MapContainer>\r\n          </div>\r\n        )}\r\n      </Card.Body>\r\n    </Card>\r\n  );\r\n}\r\n\r\nexport default DefectMap; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,QAAQ,eAAe;AACtE,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,0BAA0B;AACjC,OAAOC,CAAC,MAAM,SAAS;AACvB,SAASC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,QAAQ,iBAAiB;AAC9D,SAASC,IAAI,QAAQ,kBAAkB;;AAEvC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAOR,CAAC,CAACS,IAAI,CAACC,OAAO,CAACC,SAAS,CAACC,WAAW;AAC3CZ,CAAC,CAACS,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC;EAC1BC,aAAa,EAAE,gFAAgF;EAC/FC,OAAO,EAAE,6EAA6E;EACtFC,SAAS,EAAE;AACb,CAAC,CAAC;;AAEF;AACA,MAAMC,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,OAAO,GAAG,KAAK,KAAK;EACnD,MAAMC,QAAQ,GAAGD,OAAO,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;;EAE9C;EACA,MAAME,UAAU,GAAGF,OAAO;EACxB;EACA,qEAAqED,KAAK,YAAYE,QAAQ,CAAC,CAAC,CAAC,eAAeA,QAAQ,CAAC,CAAC,CAAC;AAC/H;AACA;AACA;AACA,8CAA8CF,KAAK;AACnD,sDAAsDA,KAAK;AAC3D,WAAW;EACP;EACA,qEAAqEA,KAAK,YAAYE,QAAQ,CAAC,CAAC,CAAC,eAAeA,QAAQ,CAAC,CAAC,CAAC;AAC/H;AACA;AACA,WAAW;EAET,OAAOpB,CAAC,CAACsB,IAAI,CAAC;IACZP,OAAO,EAAE,oCAAoCQ,kBAAkB,CAACF,UAAU,CAAC,EAAE;IAC7ED,QAAQ,EAAEA,QAAQ;IAClBI,UAAU,EAAE,CAACJ,QAAQ,CAAC,CAAC,CAAC,GAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;IACxCK,WAAW,EAAE,CAAC,CAAC,EAAE,CAACL,QAAQ,CAAC,CAAC,CAAC;EAC/B,CAAC,CAAC;AACJ,CAAC;AAED,MAAMM,KAAK,GAAG;EACZC,OAAO,EAAEV,gBAAgB,CAAC,SAAS,CAAC;EAAE;EACtCW,KAAK,EAAEX,gBAAgB,CAAC,SAAS,CAAC;EAAI;EACtCY,IAAI,EAAEZ,gBAAgB,CAAC,SAAS,CAAC;EAAK;EACtC;EACA,eAAe,EAAEA,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC;EAAE;EACpD,aAAa,EAAEA,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC;EAAI;EACpD,YAAY,EAAEA,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAK;AACtD,CAAC;AAED,SAASa,SAASA,CAAC;EAAEC;AAAK,CAAC,EAAE;EAAAC,EAAA;EAC3B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2C,KAAK,EAAEC,QAAQ,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC6C,MAAM,CAAC,GAAG7C,QAAQ,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC8C,IAAI,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE5B;EACA,MAAM,CAAC+C,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmD,YAAY,EAAEC,eAAe,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqD,SAAS,EAAEC,YAAY,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACuD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxD,QAAQ,CAAC;IACzDiC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;EACA,MAAMsB,eAAe,GAAG,MAAAA,CAAOC,YAAY,GAAG,KAAK,KAAK;IACtD,IAAI;MACFhB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;MAEhB;MACA,IAAIe,MAAM,GAAG,CAAC,CAAC;MACf,IAAIZ,SAAS,EAAEY,MAAM,CAACC,UAAU,GAAGb,SAAS;MAC5C,IAAIE,OAAO,EAAEU,MAAM,CAACE,QAAQ,GAAGZ,OAAO;MACtC,IAAIE,YAAY,EAAEQ,MAAM,CAACG,QAAQ,GAAGX,YAAY;MAChD,IAAId,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0B,IAAI,EAAEJ,MAAM,CAACK,SAAS,GAAG3B,IAAI,CAAC0B,IAAI;;MAE5C;MACA,IAAIL,YAAY,EAAE;QAChBC,MAAM,CAACM,EAAE,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MACxB;MAEAC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEV,MAAM,CAAC;MACxD,MAAMW,QAAQ,GAAG,MAAMjE,KAAK,CAACkE,GAAG,CAAC,4BAA4B,EAAE;QAAEZ;MAAO,CAAC,CAAC;MAC1ES,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEC,QAAQ,CAACE,IAAI,CAAC;MAE3C,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB;QACA,MAAMC,gBAAgB,GAAG,EAAE;QAE3BJ,QAAQ,CAACE,IAAI,CAACG,MAAM,CAACC,OAAO,CAACC,KAAK,IAAI;UACpC,IAAI;YACF;YACA,IAAIA,KAAK,CAACC,WAAW,IAAID,KAAK,CAACC,WAAW,KAAK,eAAe,EAAE;cAAA,IAAAC,gBAAA;cAC9D,IAAIC,GAAG,EAAEC,GAAG;;cAEZ;cACA,KAAAF,gBAAA,GAAIF,KAAK,CAACK,SAAS,cAAAH,gBAAA,eAAfA,gBAAA,CAAiBI,eAAe,EAAE;gBACpCH,GAAG,GAAGH,KAAK,CAACK,SAAS,CAACC,eAAe,CAACC,QAAQ;gBAC9CH,GAAG,GAAGJ,KAAK,CAACK,SAAS,CAACC,eAAe,CAACE,SAAS;gBAC/CjB,OAAO,CAACC,GAAG,CAAC,qCAAqCQ,KAAK,CAACS,QAAQ,MAAMN,GAAG,KAAKC,GAAG,GAAG,CAAC;cACtF,CAAC,MAAM;gBACL;gBACA;gBACA,IAAI,OAAOJ,KAAK,CAACC,WAAW,KAAK,QAAQ,EAAE;kBACzC;kBACA,MAAMS,MAAM,GAAGV,KAAK,CAACC,WAAW,CAACU,KAAK,CAAC,GAAG,CAAC;kBAC3C,IAAID,MAAM,CAACE,MAAM,KAAK,CAAC,EAAE;oBACvBT,GAAG,GAAGU,UAAU,CAACH,MAAM,CAAC,CAAC,CAAC,CAACI,IAAI,CAAC,CAAC,CAAC;oBAClCV,GAAG,GAAGS,UAAU,CAACH,MAAM,CAAC,CAAC,CAAC,CAACI,IAAI,CAAC,CAAC,CAAC;kBACpC;gBACF,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAAChB,KAAK,CAACC,WAAW,CAAC,IAAID,KAAK,CAACC,WAAW,CAACW,MAAM,KAAK,CAAC,EAAE;kBAC7E;kBACAT,GAAG,GAAGU,UAAU,CAACb,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC;kBACtCG,GAAG,GAAGS,UAAU,CAACb,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC;gBACxC,CAAC,MAAM,IAAI,OAAOD,KAAK,CAACC,WAAW,KAAK,QAAQ,IAAID,KAAK,CAACC,WAAW,CAACE,GAAG,IAAIH,KAAK,CAACC,WAAW,CAACG,GAAG,EAAE;kBAClG;kBACAD,GAAG,GAAGU,UAAU,CAACb,KAAK,CAACC,WAAW,CAACE,GAAG,CAAC;kBACvCC,GAAG,GAAGS,UAAU,CAACb,KAAK,CAACC,WAAW,CAACG,GAAG,CAAC;gBACzC;gBACAb,OAAO,CAACC,GAAG,CAAC,mCAAmCQ,KAAK,CAACS,QAAQ,MAAMN,GAAG,KAAKC,GAAG,GAAG,CAAC;cACpF;;cAEF;cACA,IAAI,CAACa,KAAK,CAACd,GAAG,CAAC,IAAI,CAACc,KAAK,CAACb,GAAG,CAAC,IAC1BD,GAAG,IAAI,CAAC,EAAE,IAAIA,GAAG,IAAI,EAAE,IACvBC,GAAG,IAAI,CAAC,GAAG,IAAIA,GAAG,IAAI,GAAG,EAAE;gBAC7Bb,OAAO,CAACC,GAAG,CAAC,iCAAiCQ,KAAK,CAACkB,EAAE,MAAMf,GAAG,KAAKC,GAAG,GAAG,CAAC;gBAC1EP,gBAAgB,CAACsB,IAAI,CAAC;kBACpBD,EAAE,EAAElB,KAAK,CAACkB,EAAE;kBACZT,QAAQ,EAAET,KAAK,CAACS,QAAQ;kBACxBW,IAAI,EAAEpB,KAAK,CAACoB,IAAI;kBAChBC,QAAQ,EAAE,CAAClB,GAAG,EAAEC,GAAG,CAAC;kBACpBkB,YAAY,EAAEtB,KAAK,CAACsB,YAAY;kBAChCC,SAAS,EAAE,IAAIlC,IAAI,CAACW,KAAK,CAACuB,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC;kBACrDvC,QAAQ,EAAEe,KAAK,CAACf,QAAQ;kBACxBwC,iBAAiB,EAAEzB,KAAK,CAACyB,iBAAiB;kBAC1C;kBACAC,WAAW,EAAE1B,KAAK,CAAC0B,WAAW;kBAC9B;kBACAC,gBAAgB,EAAE3B,KAAK,CAAC2B,gBAAgB;kBACxC;kBACAtB,SAAS,EAAEL,KAAK,CAACK,SAAS,IAAI,CAAC,CAAC;kBAChCuB,QAAQ,EAAE5B,KAAK,CAAC4B,QAAQ,IAAI,CAAC,CAAC;kBAC9BC,UAAU,EAAE7B,KAAK,CAAC6B,UAAU,IAAI,OAAO;kBACvCC,uBAAuB,EAAE9B,KAAK,CAAC8B;gBACjC,CAAC,CAAC;cACJ,CAAC,MAAM;gBACLvC,OAAO,CAACwC,IAAI,CAAC,mCAAmC/B,KAAK,CAACkB,EAAE,GAAG,EAAElB,KAAK,CAACC,WAAW,EAAE,eAAeE,GAAG,SAASC,GAAG,EAAE,CAAC;cACnH;YACF,CAAC,MAAM;cACLb,OAAO,CAACC,GAAG,CAAC,qBAAqBQ,KAAK,CAACkB,EAAE,iBAAiBlB,KAAK,CAACC,WAAW,EAAE,CAAC;YAChF;UACA,CAAC,CAAC,OAAO+B,UAAU,EAAE;YACnBzC,OAAO,CAACzB,KAAK,CAAC,4CAA4CkC,KAAK,CAACkB,EAAE,GAAG,EAAEc,UAAU,EAAEhC,KAAK,CAACC,WAAW,CAAC;UACvG;QACF,CAAC,CAAC;QAEFV,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEK,gBAAgB,CAACe,MAAM,CAAC;QAC1DjD,UAAU,CAACkC,gBAAgB,CAAC;;QAE5B;QACA,IAAIA,gBAAgB,CAACe,MAAM,KAAK,CAAC,EAAE;UACjC7C,QAAQ,CAAC,qEAAqE,CAAC;QACjF;MACF,CAAC,MAAM;QACLwB,OAAO,CAACzB,KAAK,CAAC,6BAA6B,EAAE2B,QAAQ,CAACE,IAAI,CAAC;QAC3D5B,QAAQ,CAAC,8BAA8B,IAAI0B,QAAQ,CAACE,IAAI,CAACsC,OAAO,IAAI,eAAe,CAAC,CAAC;MACvF;MAEApE,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOqE,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZ7C,OAAO,CAACzB,KAAK,CAAC,6BAA6B,EAAEoE,GAAG,CAAC;MACjDnE,QAAQ,CAAC,8BAA8B,IAAI,EAAAoE,aAAA,GAAAD,GAAG,CAACzC,QAAQ,cAAA0C,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcxC,IAAI,cAAAyC,kBAAA,uBAAlBA,kBAAA,CAAoBH,OAAO,KAAIC,GAAG,CAACD,OAAO,CAAC,CAAC;MACvFpE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMwE,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMvD,MAAM,GAAG,CAAC,CAAC;MACjB,IAAItB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0B,IAAI,EAAEJ,MAAM,CAACK,SAAS,GAAG3B,IAAI,CAAC0B,IAAI;MAE5C,MAAMO,QAAQ,GAAG,MAAMjE,KAAK,CAACkE,GAAG,CAAC,gBAAgB,EAAE;QAAEZ;MAAO,CAAC,CAAC;MAC9D,IAAIW,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBnB,YAAY,CAACgB,QAAQ,CAACE,IAAI,CAAC2C,KAAK,CAAC;MACnC;IACF,CAAC,CAAC,OAAOxE,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;;EAED;EACA5C,SAAS,CAAC,MAAM;IACd,MAAMqH,WAAW,GAAG,IAAIlD,IAAI,CAAC,CAAC;IAC9B,MAAMmD,aAAa,GAAG,IAAInD,IAAI,CAAC,CAAC;IAChCmD,aAAa,CAACC,OAAO,CAACD,aAAa,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;IAEnDrE,UAAU,CAACkE,WAAW,CAACI,WAAW,CAAC,CAAC,CAAChC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnDxC,YAAY,CAACqE,aAAa,CAACG,WAAW,CAAC,CAAC,CAAChC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAEvDpB,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;EAChD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAtE,SAAS,CAAC,MAAM;IACd0D,eAAe,CAAC,CAAC;IACjByD,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAC7E,IAAI,CAAC,CAAC;;EAEV;EACAtC,SAAS,CAAC,MAAM;IACd,MAAM0H,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCjE,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;IACzB,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAMkE,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAAC1E,SAAS,EAAEE,OAAO,EAAEE,YAAY,EAAEd,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B,IAAI,CAAC,CAAC;;EAElD;EACA,MAAM6D,aAAa,GAAGA,CAAA,KAAM;IAC1BnE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMoE,kBAAkB,GAAGA,CAAA,KAAM;IAC/BpE,eAAe,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAMqE,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACA,MAAMV,WAAW,GAAG,IAAIlD,IAAI,CAAC,CAAC;IAC9B,MAAMmD,aAAa,GAAG,IAAInD,IAAI,CAAC,CAAC;IAChCmD,aAAa,CAACC,OAAO,CAACD,aAAa,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;IAEnDrE,UAAU,CAACkE,WAAW,CAACI,WAAW,CAAC,CAAC,CAAChC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnDxC,YAAY,CAACqE,aAAa,CAACG,WAAW,CAAC,CAAC,CAAChC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACvDpC,eAAe,CAAC,EAAE,CAAC;IACnBI,oBAAoB,CAAC;MACnBvB,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE;IACR,CAAC,CAAC;;IAEF;IACAsB,eAAe,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAMsE,4BAA4B,GAAI9B,IAAI,IAAK;IAC7CzC,oBAAoB,CAACwE,WAAW,KAAK;MACnC,GAAGA,WAAW;MACd,CAAC/B,IAAI,GAAG,CAAC+B,WAAW,CAAC/B,IAAI;IAC3B,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMgC,eAAe,GAAG1F,OAAO,CAAC2F,MAAM,CAACC,MAAM,IAC3C5E,iBAAiB,CAAC4E,MAAM,CAAClC,IAAI,CAC/B,CAAC;EAED,oBACEnF,OAAA,CAACP,IAAI;IAAC6H,SAAS,EAAC,+BAA+B;IAAAC,QAAA,gBAC7CvH,OAAA,CAACP,IAAI,CAAC+H,MAAM;MAACF,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eAC5CvH,OAAA;QAAIsH,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAe;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eACd5H,OAAA,CAACP,IAAI,CAACoI,IAAI;MAAAN,QAAA,gBACRvH,OAAA,CAACN,GAAG;QAAC4H,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBvH,OAAA,CAACL,GAAG;UAACmI,EAAE,EAAE,CAAE;UAACR,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBACpCvH,OAAA;YAAAuH,QAAA,EAAI;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpB5H,OAAA;YAAKsH,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BvH,OAAA;cAAKsH,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCvH,OAAA;gBAAKsH,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BvH,OAAA,CAACJ,IAAI,CAACmI,KAAK;kBAAAR,QAAA,gBACTvH,OAAA,CAACJ,IAAI,CAACoI,KAAK;oBAACV,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrD5H,OAAA,CAACJ,IAAI,CAACqI,OAAO;oBACX9C,IAAI,EAAC,MAAM;oBACX+C,KAAK,EAAEjG,SAAU;oBACjBkG,QAAQ,EAAGC,CAAC,IAAKlG,YAAY,CAACkG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAC9CI,IAAI,EAAC;kBAAI;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN5H,OAAA;gBAAKsH,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BvH,OAAA,CAACJ,IAAI,CAACmI,KAAK;kBAAAR,QAAA,gBACTvH,OAAA,CAACJ,IAAI,CAACoI,KAAK;oBAACV,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACnD5H,OAAA,CAACJ,IAAI,CAACqI,OAAO;oBACX9C,IAAI,EAAC,MAAM;oBACX+C,KAAK,EAAE/F,OAAQ;oBACfgG,QAAQ,EAAGC,CAAC,IAAKhG,UAAU,CAACgG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAC5CI,IAAI,EAAC;kBAAI;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN5H,OAAA,CAACL,GAAG;UAACmI,EAAE,EAAE,CAAE;UAACR,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBACpCvH,OAAA;YAAAuH,QAAA,EAAI;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpB5H,OAAA;YAAKsH,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BvH,OAAA;cAAKsH,SAAS,EAAC,wBAAwB;cAAAC,QAAA,eACrCvH,OAAA;gBAAKsH,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BvH,OAAA,CAACJ,IAAI,CAACmI,KAAK;kBAAAR,QAAA,gBACTvH,OAAA,CAACJ,IAAI,CAACoI,KAAK;oBAACV,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACtD5H,OAAA,CAACJ,IAAI,CAAC2I,MAAM;oBACVL,KAAK,EAAE7F,YAAa;oBACpB8F,QAAQ,EAAGC,CAAC,IAAK9F,eAAe,CAAC8F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBACjDI,IAAI,EAAC,IAAI;oBAAAf,QAAA,gBAETvH,OAAA;sBAAQkI,KAAK,EAAC,EAAE;sBAAAX,QAAA,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAClCrF,SAAS,CAACiG,GAAG,CAAC,CAACjH,IAAI,EAAEkH,KAAK,kBACzBzI,OAAA;sBAAoBkI,KAAK,EAAE3G,IAAI,CAACyB,QAAS;sBAAAuE,QAAA,GACtChG,IAAI,CAACyB,QAAQ,EAAC,IAAE,EAACzB,IAAI,CAAC0B,IAAI,EAAC,GAC9B;oBAAA,GAFawF,KAAK;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEV,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN5H,OAAA,CAACL,GAAG;UAACmI,EAAE,EAAE,CAAE;UAACR,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBACpCvH,OAAA;YAAAuH,QAAA,EAAI;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3B5H,OAAA;YAAKsH,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClDvH,OAAA;cAAKsH,SAAS,EAAC,4BAA4B;cAACoB,KAAK,EAAE;gBAAEC,SAAS,EAAE,GAAG;gBAAEC,WAAW,EAAE,GAAG;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAQ,CAAE;cAAAvB,QAAA,gBACxHvH,OAAA,CAACJ,IAAI,CAACmJ,KAAK;gBACT5D,IAAI,EAAC,UAAU;gBACfF,EAAE,EAAC,gBAAgB;gBACnB+D,KAAK,EAAC,UAAU;gBAChBC,OAAO,EAAExG,iBAAiB,CAACtB,OAAQ;gBACnCgH,QAAQ,EAAEA,CAAA,KAAMlB,4BAA4B,CAAC,SAAS,CAAE;gBACxDK,SAAS,EAAC;cAAsB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACF5H,OAAA,CAACJ,IAAI,CAACmJ,KAAK;gBACT5D,IAAI,EAAC,UAAU;gBACfF,EAAE,EAAC,cAAc;gBACjB+D,KAAK,EAAC,QAAQ;gBACdC,OAAO,EAAExG,iBAAiB,CAACrB,KAAM;gBACjC+G,QAAQ,EAAEA,CAAA,KAAMlB,4BAA4B,CAAC,OAAO,CAAE;gBACtDK,SAAS,EAAC;cAAsB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACF5H,OAAA,CAACJ,IAAI,CAACmJ,KAAK;gBACT5D,IAAI,EAAC,UAAU;gBACfF,EAAE,EAAC,aAAa;gBAChB+D,KAAK,EAAC,OAAO;gBACbC,OAAO,EAAExG,iBAAiB,CAACpB,IAAK;gBAChC8G,QAAQ,EAAEA,CAAA,KAAMlB,4BAA4B,CAAC,MAAM,CAAE;gBACrDK,SAAS,EAAC;cAAsB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5H,OAAA,CAACN,GAAG;QAAC4H,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBvH,OAAA,CAACL,GAAG;UAAC2H,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BvH,OAAA;YAAKsH,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCvH,OAAA,CAACH,MAAM;cACLqJ,OAAO,EAAC,SAAS;cACjBZ,IAAI,EAAC,IAAI;cACTa,OAAO,EAAEpC,kBAAmB;cAC5BO,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC5B;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT5H,OAAA,CAACH,MAAM;cACLqJ,OAAO,EAAC,mBAAmB;cAC3BZ,IAAI,EAAC,IAAI;cACTa,OAAO,EAAEnC,kBAAmB;cAC5BM,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC5B;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT5H,OAAA,CAACH,MAAM;cACLqJ,OAAO,EAAC,SAAS;cACjBZ,IAAI,EAAC,IAAI;cACTa,OAAO,EAAErC,aAAc;cACvBsC,QAAQ,EAAEzH,OAAQ;cAClB2F,SAAS,EAAC,YAAY;cAAAC,QAAA,EAErB5F,OAAO,GAAG,kBAAkB,GAAG;YAAgB;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5H,OAAA,CAACN,GAAG;QAAA6H,QAAA,eACFvH,OAAA,CAACL,GAAG;UAAA4H,QAAA,eACFvH,OAAA;YAAKsH,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BvH,OAAA;cAAKsH,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BvH,OAAA;gBAAIsH,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3C5H,OAAA;gBAAKsH,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BvH,OAAA;kBAAKsH,SAAS,EAAC,eAAe;kBAACoB,KAAK,EAAE;oBAAEW,eAAe,EAAE;kBAAU;gBAAE;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5E5H,OAAA;kBAAAuH,QAAA,EAAM;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACN5H,OAAA;gBAAKsH,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BvH,OAAA;kBAAKsH,SAAS,EAAC,eAAe;kBAACoB,KAAK,EAAE;oBAAEW,eAAe,EAAE;kBAAU;gBAAE;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5E5H,OAAA;kBAAAuH,QAAA,EAAM;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACN5H,OAAA;gBAAKsH,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BvH,OAAA;kBAAKsH,SAAS,EAAC,eAAe;kBAACoB,KAAK,EAAE;oBAAEW,eAAe,EAAE;kBAAU;gBAAE;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5E5H,OAAA;kBAAAuH,QAAA,EAAM;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5H,OAAA;cAAKsH,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BvH,OAAA;gBAAIsH,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3C5H,OAAA;gBAAKsH,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BvH,OAAA;kBAAKsH,SAAS,EAAC,4BAA4B;kBAACoB,KAAK,EAAE;oBAAEW,eAAe,EAAE;kBAAU,CAAE;kBAAA9B,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3F5H,OAAA;kBAAAuH,QAAA,EAAM;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACN5H,OAAA;gBAAKsH,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BvH,OAAA;kBAAKsH,SAAS,EAAC,4BAA4B;kBAACoB,KAAK,EAAE;oBAAEW,eAAe,EAAE;kBAAU,CAAE;kBAAA9B,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3F5H,OAAA;kBAAAuH,QAAA,EAAM;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACN5H,OAAA;gBAAKsH,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BvH,OAAA;kBAAKsH,SAAS,EAAC,4BAA4B;kBAACoB,KAAK,EAAE;oBAAEW,eAAe,EAAE;kBAAU,CAAE;kBAAA9B,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3F5H,OAAA;kBAAAuH,QAAA,EAAM;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELjG,OAAO,gBACN3B,OAAA;QAAKsH,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BvH,OAAA;UAAKsH,SAAS,EAAC,6BAA6B;UAACrE,IAAI,EAAC,QAAQ;UAAAsE,QAAA,eACxDvH,OAAA;YAAMsH,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GACJ/F,KAAK,gBACP7B,OAAA;QAAKsH,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAE1F;MAAK;QAAA4F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,gBAEjD5H,OAAA;QAAKsH,SAAS,EAAC,eAAe;QAACoB,KAAK,EAAE;UAAEY,MAAM,EAAE,OAAO;UAAET,KAAK,EAAE;QAAO,CAAE;QAAAtB,QAAA,eACvEvH,OAAA,CAACb,YAAY;UACX4C,MAAM,EAAEA,MAAO;UACfC,IAAI,EAAEA,IAAK;UACX0G,KAAK,EAAE;YAAEY,MAAM,EAAE,MAAM;YAAET,KAAK,EAAE;UAAO,CAAE;UACzCU,eAAe,EAAE,IAAK;UAAAhC,QAAA,gBAEtBvH,OAAA,CAACZ,SAAS;YACRoK,WAAW,EAAC,yFAAyF;YACrGC,GAAG,EAAC;UAAoD;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,EAEDT,eAAe,CAACqB,GAAG,CAAEnB,MAAM,IAAK;YAAA,IAAAqC,iBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA;YAC/B;YACA,MAAMC,OAAO,GAAGnD,MAAM,CAACzB,UAAU,KAAK,OAAO,GAAG,GAAGyB,MAAM,CAAClC,IAAI,QAAQ,GAAGkC,MAAM,CAAClC,IAAI;YACpF,MAAMsF,YAAY,GAAGvJ,KAAK,CAACsJ,OAAO,CAAC,IAAItJ,KAAK,CAACmG,MAAM,CAAClC,IAAI,CAAC;YAEzD,oBACAnF,OAAA,CAACX,MAAM;cAEL+F,QAAQ,EAAEiC,MAAM,CAACjC,QAAS;cAC1BtE,IAAI,EAAE2J,YAAa;cAAAlD,QAAA,eAEnBvH,OAAA,CAACV,KAAK;gBAACoL,QAAQ,EAAE,GAAI;gBAAAnD,QAAA,eACnBvH,OAAA;kBAAKsH,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BvH,OAAA;oBAAAuH,QAAA,GAAKF,MAAM,CAAClC,IAAI,CAACwF,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGvD,MAAM,CAAClC,IAAI,CAAC0F,KAAK,CAAC,CAAC,CAAC,EAAC,SAAO;kBAAA;oBAAApD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAG3EP,MAAM,CAACzB,UAAU,KAAK,OAAO,IAAIyB,MAAM,CAACyD,oBAAoB,iBAC3D9K,OAAA;oBAAKsH,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BvH,OAAA;sBACE+K,GAAG,EAAE,0BAA0B1D,MAAM,CAACyD,oBAAoB,EAAG;sBAC7DE,GAAG,EAAC,iBAAiB;sBACrB1D,SAAS,EAAC,0BAA0B;sBACpCoB,KAAK,EAAE;wBAAEuC,SAAS,EAAE,OAAO;wBAAEP,QAAQ,EAAE;sBAAO,CAAE;sBAChDQ,OAAO,EAAG9C,CAAC,IAAK;wBACd9E,OAAO,CAACwC,IAAI,CAAC,iDAAiDuB,MAAM,CAAC7C,QAAQ,EAAE,CAAC;wBAChF4D,CAAC,CAACC,MAAM,CAACK,KAAK,CAACyC,OAAO,GAAG,MAAM;sBACjC;oBAAE;sBAAA1D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACF5H,OAAA;sBAAKsH,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACnBvH,OAAA;wBAAOsH,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,EAAC;sBAA6B;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,EACzEP,MAAM,CAAC+D,QAAQ,iBACdpL,OAAA;wBAAAuH,QAAA,eACEvH,OAAA;0BAAOsH,SAAS,EAAC,oBAAoB;0BAAAC,QAAA,GAAC,YAAU,EAACF,MAAM,CAAC+D,QAAQ;wBAAA;0BAAA3D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtE,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,EAGAP,MAAM,CAACzB,UAAU,KAAK,OAAO,IAAIyB,MAAM,CAACxB,uBAAuB,iBAC9D7F,OAAA;oBAAKsH,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BvH,OAAA;sBACE+K,GAAG,EAAE1D,MAAM,CAACxB,uBAAwB;sBACpCmF,GAAG,EAAC,cAAc;sBAClB1D,SAAS,EAAC,0BAA0B;sBACpCoB,KAAK,EAAE;wBAAEuC,SAAS,EAAE,OAAO;wBAAEP,QAAQ,EAAE;sBAAO,CAAE;sBAChDQ,OAAO,EAAG9C,CAAC,IAAK;wBACd9E,OAAO,CAACwC,IAAI,CAAC,mCAAmCuB,MAAM,CAAC7C,QAAQ,EAAE,CAAC;wBAClE4D,CAAC,CAACC,MAAM,CAACK,KAAK,CAACyC,OAAO,GAAG,MAAM;sBACjC;oBAAE;sBAAA1D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACF5H,OAAA;sBAAKsH,SAAS,EAAC,MAAM;sBAAAC,QAAA,eACnBvH,OAAA;wBAAOsH,SAAS,EAAC,sBAAsB;wBAAAC,QAAA,EAAC;sBAAiB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,eAGD5H,OAAA;oBAAKsH,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBvH,OAAA;sBAAIsH,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnD5H,OAAA;sBAAIsH,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,gBACjCvH,OAAA;wBAAAuH,QAAA,gBAAIvH,OAAA;0BAAAuH,QAAA,EAAQ;wBAAM;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAACP,MAAM,CAAChC,YAAY;sBAAA;wBAAAoC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACtD5H,OAAA;wBAAAuH,QAAA,gBAAIvH,OAAA;0BAAAuH,QAAA,EAAQ;wBAAK;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAACP,MAAM,CAAC/B,SAAS;sBAAA;wBAAAmC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAClD5H,OAAA;wBAAAuH,QAAA,gBAAIvH,OAAA;0BAAAuH,QAAA,EAAQ;wBAAY;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAACP,MAAM,CAACrE,QAAQ;sBAAA;wBAAAyE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACxD5H,OAAA;wBAAAuH,QAAA,gBAAIvH,OAAA;0BAAAuH,QAAA,EAAQ;wBAAW;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAACP,MAAM,CAACzB,UAAU;sBAAA;wBAAA6B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACzD5H,OAAA;wBAAAuH,QAAA,gBAAIvH,OAAA;0BAAAuH,QAAA,EAAQ;wBAAI;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAACP,MAAM,CAACjC,QAAQ,CAAC,CAAC,CAAC,CAACiG,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAAChE,MAAM,CAACjC,QAAQ,CAAC,CAAC,CAAC,CAACiG,OAAO,CAAC,CAAC,CAAC;sBAAA;wBAAA5D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,EAGLP,MAAM,CAAClC,IAAI,KAAK,OAAO,IAAIkC,MAAM,CAAC5B,WAAW,iBAC5CzF,OAAA;oBAAKsH,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBvH,OAAA;sBAAIsH,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC7C5H,OAAA;sBAAIsH,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAChC+D,MAAM,CAACC,OAAO,CAAClE,MAAM,CAAC5B,WAAW,CAAC,CAAC+C,GAAG,CAAC,CAAC,CAACrD,IAAI,EAAEqG,KAAK,CAAC,kBACpDxL,OAAA;wBAAAuH,QAAA,gBAAevH,OAAA;0BAAAuH,QAAA,GAASpC,IAAI,EAAC,GAAC;wBAAA;0BAAAsC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAAC4D,KAAK;sBAAA,GAArCrG,IAAI;wBAAAsC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAsC,CACpD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CACN,EAEAP,MAAM,CAAClC,IAAI,KAAK,MAAM,IAAIkC,MAAM,CAAC3B,gBAAgB,iBAChD1F,OAAA;oBAAKsH,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBvH,OAAA;sBAAIsH,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAe;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjD5H,OAAA;sBAAIsH,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAChC+D,MAAM,CAACC,OAAO,CAAClE,MAAM,CAAC3B,gBAAgB,CAAC,CAAC8C,GAAG,CAAC,CAAC,CAACiD,SAAS,EAAED,KAAK,CAAC,kBAC9DxL,OAAA;wBAAAuH,QAAA,gBAAoBvH,OAAA;0BAAAuH,QAAA,GAASkE,SAAS,EAAC,GAAC;wBAAA;0BAAAhE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAAC4D,KAAK;sBAAA,GAA/CC,SAAS;wBAAAhE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAA2C,CAC9D;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CACN,EAGA,CAACP,MAAM,CAACjD,SAAS,IAAIiD,MAAM,CAAC1B,QAAQ,kBACnC3F,OAAA;oBAAKsH,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBvH,OAAA;sBAAIsH,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAoB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACtD5H,OAAA;sBAAKsH,SAAS,EAAC,OAAO;sBAAAC,QAAA,GAEnB,EAAAmC,iBAAA,GAAArC,MAAM,CAACjD,SAAS,cAAAsF,iBAAA,uBAAhBA,iBAAA,CAAkBrF,eAAe,kBAChCrE,OAAA;wBAAKsH,SAAS,EAAC,2BAA2B;wBAAAC,QAAA,gBACxCvH,OAAA;0BAAQsH,SAAS,EAAC,cAAc;0BAAAC,QAAA,EAAC;wBAAc;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACxD5H,OAAA;0BAAAuH,QAAA,GAAK,OAAK,GAAAoC,qBAAA,GAACtC,MAAM,CAACjD,SAAS,CAACC,eAAe,CAACC,QAAQ,cAAAqF,qBAAA,uBAAzCA,qBAAA,CAA2C0B,OAAO,CAAC,CAAC,CAAC;wBAAA;0BAAA5D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACvE5H,OAAA;0BAAAuH,QAAA,GAAK,OAAK,GAAAqC,sBAAA,GAACvC,MAAM,CAACjD,SAAS,CAACC,eAAe,CAACE,SAAS,cAAAqF,sBAAA,uBAA1CA,sBAAA,CAA4CyB,OAAO,CAAC,CAAC,CAAC;wBAAA;0BAAA5D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrE,CACN,EAGA,EAAAiC,kBAAA,GAAAxC,MAAM,CAACjD,SAAS,cAAAyF,kBAAA,uBAAhBA,kBAAA,CAAkB6B,WAAW,KAAIJ,MAAM,CAACK,IAAI,CAACtE,MAAM,CAACjD,SAAS,CAACsH,WAAW,CAAC,CAAC/G,MAAM,GAAG,CAAC,iBACpF3E,OAAA;wBAAKsH,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBACnBvH,OAAA;0BAAAuH,QAAA,EAAQ;wBAAU;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC3B5H,OAAA;0BAAIsH,SAAS,EAAC,oBAAoB;0BAAAC,QAAA,GAC/BF,MAAM,CAACjD,SAAS,CAACsH,WAAW,CAACE,WAAW,iBACvC5L,OAAA;4BAAAuH,QAAA,GAAI,QAAM,EAACF,MAAM,CAACjD,SAAS,CAACsH,WAAW,CAACE,WAAW;0BAAA;4BAAAnE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CACzD,EACAP,MAAM,CAACjD,SAAS,CAACsH,WAAW,CAACG,YAAY,iBACxC7L,OAAA;4BAAAuH,QAAA,GAAI,SAAO,EAACF,MAAM,CAACjD,SAAS,CAACsH,WAAW,CAACG,YAAY;0BAAA;4BAAApE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAC3D;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CACN,EAGA,EAAAkC,kBAAA,GAAAzC,MAAM,CAACjD,SAAS,cAAA0F,kBAAA,uBAAhBA,kBAAA,CAAkBgC,cAAc,KAAIR,MAAM,CAACK,IAAI,CAACtE,MAAM,CAACjD,SAAS,CAAC0H,cAAc,CAAC,CAACnH,MAAM,GAAG,CAAC,iBAC1F3E,OAAA;wBAAKsH,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBACnBvH,OAAA;0BAAAuH,QAAA,EAAQ;wBAAa;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC9B5H,OAAA;0BAAIsH,SAAS,EAAC,oBAAoB;0BAAAC,QAAA,GAC/BF,MAAM,CAACjD,SAAS,CAAC0H,cAAc,CAACC,GAAG,iBAClC/L,OAAA;4BAAAuH,QAAA,GAAI,OAAK,EAACF,MAAM,CAACjD,SAAS,CAAC0H,cAAc,CAACC,GAAG;0BAAA;4BAAAtE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CACnD,EACAP,MAAM,CAACjD,SAAS,CAAC0H,cAAc,CAACE,aAAa,iBAC5ChM,OAAA;4BAAAuH,QAAA,GAAI,YAAU,EAACF,MAAM,CAACjD,SAAS,CAAC0H,cAAc,CAACE,aAAa;0BAAA;4BAAAvE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAClE;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CACN,EAGA,EAAAmC,kBAAA,GAAA1C,MAAM,CAACjD,SAAS,cAAA2F,kBAAA,uBAAhBA,kBAAA,CAAkBkC,UAAU,kBAC3BjM,OAAA;wBAAKsH,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBACnBvH,OAAA;0BAAAuH,QAAA,EAAQ;wBAAc;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAACP,MAAM,CAACjD,SAAS,CAAC6H,UAAU,CAACpD,KAAK,EAAC,QAAG,EAACxB,MAAM,CAACjD,SAAS,CAAC6H,UAAU,CAAC3C,MAAM,EACxGjC,MAAM,CAACjD,SAAS,CAAC6H,UAAU,CAACC,MAAM,iBACjClM,OAAA;0BAAAuH,QAAA,GAAM,IAAE,EAACF,MAAM,CAACjD,SAAS,CAAC6H,UAAU,CAACC,MAAM,EAAC,GAAC;wBAAA;0BAAAzE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CACpD;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CACN,EAGAP,MAAM,CAACzB,UAAU,KAAK,OAAO,iBAC5B5F,OAAA;wBAAKsH,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBACnBvH,OAAA;0BAAIsH,SAAS,EAAC,WAAW;0BAAAC,QAAA,EAAC;wBAAoB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACnD5H,OAAA;0BAAIsH,SAAS,EAAC,qBAAqB;0BAAAC,QAAA,GAChC,EAAAyC,gBAAA,GAAA3C,MAAM,CAAC1B,QAAQ,cAAAqE,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBmC,WAAW,cAAAlC,qBAAA,uBAA5BA,qBAAA,CAA8BmC,QAAQ,kBACrCpM,OAAA;4BAAAuH,QAAA,gBAAIvH,OAAA;8BAAAuH,QAAA,EAAQ;4BAAS;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,KAAC,EAACyE,IAAI,CAACC,KAAK,CAACjF,MAAM,CAAC1B,QAAQ,CAACwG,WAAW,CAACC,QAAQ,CAAC,EAAC,GAAC;0BAAA;4BAAA3E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CACvF,EACA,EAAAsC,iBAAA,GAAA7C,MAAM,CAAC1B,QAAQ,cAAAuE,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CAAiB+B,UAAU,cAAA9B,qBAAA,uBAA3BA,qBAAA,CAA6BtB,KAAK,OAAAuB,iBAAA,GAAI/C,MAAM,CAAC1B,QAAQ,cAAAyE,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CAAiB6B,UAAU,cAAA5B,qBAAA,uBAA3BA,qBAAA,CAA6Bf,MAAM,kBACxEtJ,OAAA;4BAAAuH,QAAA,gBAAIvH,OAAA;8BAAAuH,QAAA,EAAQ;4BAAW;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,KAAC,EAACP,MAAM,CAAC1B,QAAQ,CAACsG,UAAU,CAACpD,KAAK,EAAC,GAAC,EAACxB,MAAM,CAAC1B,QAAQ,CAACsG,UAAU,CAAC3C,MAAM;0BAAA;4BAAA7B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAC5G,EACA,EAAA0C,iBAAA,GAAAjD,MAAM,CAAC1B,QAAQ,cAAA2E,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CAAiB6B,WAAW,cAAA5B,qBAAA,uBAA5BA,qBAAA,CAA8BgC,WAAW,kBACxCvM,OAAA;4BAAAuH,QAAA,gBAAIvH,OAAA;8BAAAuH,QAAA,EAAQ;4BAAO;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,KAAC,EAACP,MAAM,CAAC1B,QAAQ,CAACwG,WAAW,CAACI,WAAW,CAAC3B,WAAW,CAAC,CAAC;0BAAA;4BAAAnD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CACzF,EACAP,MAAM,CAAC+D,QAAQ,iBACdpL,OAAA;4BAAAuH,QAAA,gBAAIvH,OAAA;8BAAAuH,QAAA,EAAQ;4BAAS;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,KAAC,EAACP,MAAM,CAAC+D,QAAQ;0BAAA;4BAAA3D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CACrD,EACAP,MAAM,CAACmF,kBAAkB,iBACxBxM,OAAA;4BAAAuH,QAAA,gBAAIvH,OAAA;8BAAAuH,QAAA,EAAQ;4BAAe;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,cAAU;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CACnD,EACAP,MAAM,CAACoF,mBAAmB,iBACzBzM,OAAA;4BAAAuH,QAAA,gBAAIvH,OAAA;8BAAAuH,QAAA,EAAQ;4BAAgB;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,cAAU;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CACpD;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,eAGD5H,OAAA;oBAAKsH,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BvH,OAAA,CAACF,IAAI;sBACH4M,EAAE,EAAE,SAASrF,MAAM,CAAC7C,QAAQ,EAAG;sBAC/B8C,SAAS,EAAC,wBAAwB;sBAClC6B,OAAO,EAAGf,CAAC,IAAKA,CAAC,CAACuE,eAAe,CAAC,CAAE;sBAAApF,QAAA,EACrC;oBAED;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EACNP,MAAM,CAACxB,uBAAuB,iBAC7B7F,OAAA;sBACE4M,IAAI,EAAEvF,MAAM,CAACxB,uBAAwB;sBACrCwC,MAAM,EAAC,QAAQ;sBACfwE,GAAG,EAAC,qBAAqB;sBACzBvF,SAAS,EAAC,kCAAkC;sBAC5C6B,OAAO,EAAGf,CAAC,IAAKA,CAAC,CAACuE,eAAe,CAAC,CAAE;sBAAApF,QAAA,EACrC;oBAED;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CACJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC,GAhMHP,MAAM,CAACpC,EAAE;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiMR,CAAC;UAEX,CAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEX;AAACpG,EAAA,CAjmBQF,SAAS;AAAAwL,EAAA,GAATxL,SAAS;AAmmBlB,eAAeA,SAAS;AAAC,IAAAwL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}