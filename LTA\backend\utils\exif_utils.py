import PIL.Image
from PIL.ExifTags import TAGS, GPSTAGS
from PIL.TiffImagePlugin import IFDRational
import numpy as np
import io
import base64
import logging
from datetime import datetime
import math

logger = logging.getLogger(__name__)

def serialize_exif_value(value):
    """
    Convert EXIF values to MongoDB-serializable format.
    Handles IFDRational, bytes, and other non-serializable types.
    """
    try:
        if isinstance(value, IFDRational):
            # Convert IFDRational to float
            if value.denominator == 0:
                return float('inf') if value.numerator > 0 else float('-inf')
            return float(value.numerator) / float(value.denominator)
        elif isinstance(value, bytes):
            # Convert bytes to string (for text fields) or skip binary data
            try:
                return value.decode('utf-8', errors='ignore')
            except:
                return f"<binary data: {len(value)} bytes>"
        elif isinstance(value, (list, tuple)):
            # Recursively serialize lists/tuples
            return [serialize_exif_value(item) for item in value]
        elif isinstance(value, dict):
            # Recursively serialize dictionaries
            return {k: serialize_exif_value(v) for k, v in value.items()}
        elif isinstance(value, (int, float, str, bool)) or value is None:
            # Handle NaN and infinity values
            if isinstance(value, float):
                if math.isnan(value):
                    return None  # Convert NaN to None for MongoDB
                elif math.isinf(value):
                    return "infinity" if value > 0 else "-infinity"
            # Already serializable
            return value
        elif hasattr(value, '__float__'):
            # Try to convert to float
            return float(value)
        elif hasattr(value, '__int__'):
            # Try to convert to int
            return int(value)
        elif hasattr(value, '__str__'):
            # Convert to string as fallback
            return str(value)
        else:
            # Last resort: convert to string
            return str(value)
    except Exception as e:
        logger.warning(f"Could not serialize EXIF value {value}: {e}")
        return str(value)

def serialize_exif_data(exif_data):
    """
    Serialize entire EXIF data dictionary for MongoDB storage.
    """
    if not isinstance(exif_data, dict):
        return exif_data

    serialized = {}
    for key, value in exif_data.items():
        try:
            serialized[str(key)] = serialize_exif_value(value)
        except Exception as e:
            logger.warning(f"Could not serialize EXIF key {key}: {e}")
            serialized[str(key)] = str(value)

    return serialized

def get_exif_data(image_data):
    """Extract EXIF data from an image.
    
    Args:
        image_data: Either a file path, file object, or base64 encoded image
    """
    exif_data = {}
    try:
        # Handle base64 encoded images
        if isinstance(image_data, str) and "base64," in image_data:
            # Extract the base64 part
            base64_data = image_data.split("base64,")[1]
            # Convert to image
            img_data = base64.b64decode(base64_data)
            img = PIL.Image.open(io.BytesIO(img_data))
        else:
            # Handle regular file paths or file objects
            img = PIL.Image.open(image_data)
        
        # Get EXIF data
        info = img._getexif()
        if info:
            for tag, value in info.items():
                decoded = TAGS.get(tag, tag)
                if decoded == "GPSInfo":
                    gps_data = {}
                    for gps_tag in value:
                        sub_decoded = GPSTAGS.get(gps_tag, gps_tag)
                        gps_data[sub_decoded] = serialize_exif_value(value[gps_tag])
                    exif_data[decoded] = gps_data
                else:
                    exif_data[decoded] = serialize_exif_value(value)
    except Exception as e:
        print(f"Error extracting EXIF data: {e}")
    return serialize_exif_data(exif_data)

def convert_to_degrees(value):
    """Convert GPS coordinates from DMS (degree, minutes, seconds) to DD (decimal degrees)."""
    if not value:
        return None

    try:
        def safe_float_conversion(val):
            """Safely convert a value to float, handling rational numbers and zero denominators."""
            if hasattr(val, 'numerator') and hasattr(val, 'denominator'):
                # Handle rational numbers (fractions)
                if val.denominator == 0:
                    logger.warning(f"GPS coordinate has zero denominator: {val}, skipping this coordinate")
                    return None  # Return None instead of 0.0 to indicate invalid data
                return float(val.numerator) / float(val.denominator)
            else:
                return float(val)

        # Convert each component safely
        d = safe_float_conversion(value[0])
        m = safe_float_conversion(value[1])
        s = safe_float_conversion(value[2])

        # If any component is None (due to zero denominator), return None
        if d is None or m is None or s is None:
            logger.warning(f"One or more GPS coordinate components are invalid: d={d}, m={m}, s={s}")
            return None

        # Calculate decimal degrees
        decimal_degrees = d + (m / 60.0) + (s / 3600.0)

        # Validate the result is within reasonable GPS bounds
        if not (-180 <= decimal_degrees <= 180):
            logger.warning(f"GPS coordinate out of valid range: {decimal_degrees}")
            return None

        return decimal_degrees

    except (ValueError, TypeError, ZeroDivisionError, IndexError) as e:
        logger.warning(f"Error converting GPS coordinates to degrees: {e}")
        return None

def get_gps_coordinates(image_data):
    """Extract GPS coordinates from image EXIF data and return them as decimal degrees."""
    try:
        exif_data = get_exif_data(image_data)
        if not exif_data or "GPSInfo" not in exif_data:
            logger.debug("No EXIF data or GPS info found in image")
            return None, None

        gps_info = exif_data["GPSInfo"]
        logger.debug(f"GPS info found: {list(gps_info.keys())}")

        lat = None
        lon = None

        # Extract latitude
        if "GPSLatitude" in gps_info and "GPSLatitudeRef" in gps_info:
            logger.debug(f"GPS Latitude data: {gps_info['GPSLatitude']}")
            logger.debug(f"GPS Latitude reference: {gps_info['GPSLatitudeRef']}")

            lat = convert_to_degrees(gps_info["GPSLatitude"])
            if lat is not None:
                # Apply hemisphere correction
                if gps_info["GPSLatitudeRef"] == "S":
                    lat = -lat
                logger.info(f"✅ Successfully extracted latitude: {lat}")
            else:
                logger.warning("❌ Failed to convert latitude to degrees")
        else:
            logger.debug("No GPS latitude data found in EXIF")

        # Extract longitude
        if "GPSLongitude" in gps_info and "GPSLongitudeRef" in gps_info:
            logger.debug(f"GPS Longitude data: {gps_info['GPSLongitude']}")
            logger.debug(f"GPS Longitude reference: {gps_info['GPSLongitudeRef']}")

            lon = convert_to_degrees(gps_info["GPSLongitude"])
            if lon is not None:
                # Apply hemisphere correction
                if gps_info["GPSLongitudeRef"] == "W":
                    lon = -lon
                logger.info(f"✅ Successfully extracted longitude: {lon}")
            else:
                logger.warning("❌ Failed to convert longitude to degrees")
        else:
            logger.debug("No GPS longitude data found in EXIF")

        # Final validation
        if lat is not None and lon is not None:
            # Validate coordinate ranges
            if not (-90 <= lat <= 90):
                logger.error(f"Invalid latitude value: {lat} (must be between -90 and 90)")
                lat = None
            if not (-180 <= lon <= 180):
                logger.error(f"Invalid longitude value: {lon} (must be between -180 and 180)")
                lon = None

            if lat is not None and lon is not None:
                logger.info(f"🎯 Final GPS coordinates: ({lat:.6f}, {lon:.6f})")

        return lat, lon

    except Exception as e:
        logger.error(f"Error extracting GPS coordinates: {e}")
        return None, None

def get_comprehensive_exif_data(image_data):
    """Extract comprehensive EXIF data including GPS, camera info, and timestamp."""
    try:
        # Handle base64 encoded images
        if isinstance(image_data, str) and "base64," in image_data:
            # Extract the base64 part
            base64_data = image_data.split("base64,")[1]
            # Convert to image
            img_data = base64.b64decode(base64_data)
            img = PIL.Image.open(io.BytesIO(img_data))
        else:
            # Handle regular file paths or file objects
            img = PIL.Image.open(image_data)

        # Get basic image info
        basic_info = {
            'width': img.width,
            'height': img.height,
            'format': img.format,
            'mode': img.mode
        }

        # Get EXIF data
        exif_data = {}
        try:
            exif_dict = img._getexif()
            if exif_dict:
                for tag, value in exif_dict.items():
                    decoded = TAGS.get(tag, tag)
                    if decoded == "GPSInfo":
                        gps_data = {}
                        for gps_tag in value:
                            sub_decoded = GPSTAGS.get(gps_tag, gps_tag)
                            gps_data[sub_decoded] = serialize_exif_value(value[gps_tag])
                        exif_data[decoded] = gps_data
                    else:
                        exif_data[decoded] = serialize_exif_value(value)
        except Exception as e:
            logger.warning(f"Error extracting EXIF data: {e}")

        # Extract specific metadata
        metadata = {
            'basic_info': basic_info,
            'exif_data': exif_data,
            'gps_coordinates': None,
            'timestamp': None,
            'camera_info': {},
            'technical_info': {}
        }

        # Extract GPS coordinates
        if 'GPSInfo' in exif_data:
            lat, lon = get_gps_coordinates(image_data)
            if lat is not None and lon is not None:
                metadata['gps_coordinates'] = {
                    'latitude': lat,
                    'longitude': lon,
                    'coordinates_string': f"{lat},{lon}"
                }

        # Extract timestamp
        timestamp_fields = ['DateTime', 'DateTimeOriginal', 'DateTimeDigitized']
        for field in timestamp_fields:
            if field in exif_data:
                try:
                    timestamp = datetime.strptime(exif_data[field], '%Y:%m:%d %H:%M:%S')
                    metadata['timestamp'] = timestamp.isoformat()
                    break
                except ValueError:
                    continue

        # Extract camera information
        camera_fields = {
            'Make': 'camera_make',
            'Model': 'camera_model',
            'Software': 'software',
            'LensModel': 'lens_model',
            'LensMake': 'lens_make'
        }

        for exif_field, meta_field in camera_fields.items():
            if exif_field in exif_data:
                metadata['camera_info'][meta_field] = str(exif_data[exif_field])

        # Extract technical information
        technical_fields = {
            'ExposureTime': 'exposure_time',
            'FNumber': 'f_number',
            'ISO': 'iso',
            'ISOSpeedRatings': 'iso_speed',
            'FocalLength': 'focal_length',
            'Flash': 'flash',
            'WhiteBalance': 'white_balance',
            'ExposureMode': 'exposure_mode',
            'SceneCaptureType': 'scene_type'
        }

        for exif_field, meta_field in technical_fields.items():
            if exif_field in exif_data:
                metadata['technical_info'][meta_field] = str(exif_data[exif_field])

        # Serialize EXIF data for MongoDB compatibility
        metadata['exif_data'] = serialize_exif_data(exif_data)

        return metadata

    except Exception as e:
        logger.error(f"Error extracting comprehensive EXIF data: {e}")
        return {
            'basic_info': {},
            'exif_data': {},
            'gps_coordinates': None,
            'timestamp': None,
            'camera_info': {},
            'technical_info': {}
        }

def format_coordinates(lat, lon):
    """Format coordinates as a string."""
    if lat is None or lon is None:
        return "Not Available"
    
    return f"{lat:.6f}, {lon:.6f}" 